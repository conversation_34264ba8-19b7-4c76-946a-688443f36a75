{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "$require", "name", "$module", "call", "exports", "util", "global", "define", "amd", "<PERSON>", "isLong", "configure", "module", "1", "require", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "Number", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "writeFloat_ieee754", "writeUint", "val", "buf", "pos", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "writeFloat_f32_cpy", "f32", "f8b", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "f64", "le", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "Float32Array", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "inquire", "moduleName", "mod", "eval", "e", "isAbsolute", "path", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "utf8", "len", "read", "write", "c1", "c2", "common", "commonRe", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "timeType", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "Enum", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "defaultAlreadyEmitted", "resolvedType", "typeDefault", "repeated", "fullName", "isUnsigned", "genValuePartial_toObject", "converter", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "arrayDefault", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "hasKs2", "_fieldsArray", "indexOf", "filter", "ref", "types", "defaults", "basic", "packed", "delimited", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "ReflectionObject", "Namespace", "create", "constructor", "className", "comment", "comments", "valuesOptions", "TypeError", "_valuesFeatures", "reserved", "_resolveFeatures", "edition", "_edition", "for<PERSON>ach", "key", "parentFeaturesCopy", "assign", "_features", "features", "fromJSON", "enm", "_defaultEdition", "toJSON", "toJSONOptions", "keepComments", "Boolean", "_editionToJSON", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "extensionField", "declaringField", "defineProperty", "field_presence", "message_encoding", "repeated_field_encoding", "setOption", "ifNotSet", "resolved", "parent", "lookupTypeOrEnum", "proto3_optional", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "_inferLegacyProtoFeatures", "pop", "group", "getOption", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "Writer", "BufferWriter", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "parsedOptions", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "_lookupCache", "_needsRecursiveFeatureResolution", "_needsRecursiveResolve", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "isArray", "ptr", "part", "resolveAll", "_resolveFeaturesRecursive", "lookup", "filterTypes", "parentAlreadyChecked", "flatPath", "found", "_fullyQualifiedObjects", "_lookupImpl", "current", "hasOwnProperty", "exact", "lookupEnum", "lookupService", "Service_", "Enum_", "editions2023Defaults", "enum_type", "json_format", "utf8_validation", "proto2Defaults", "proto3Defaults", "_featuresResolved", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "protoFeatures", "lexicalParentFeaturesCopy", "setProperty", "setParsedOption", "propName", "opt", "newOpt", "find", "newValue", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "pkg", "imports", "weakImports", "token", "whichImports", "preferTrailingComment", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "topLevelObjects", "topLevelOptions", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "parseNumber", "substring", "parseInt", "parseFloat", "readRanges", "target", "acceptStrings", "parseId", "str", "dummy", "ifBlock", "parseOption", "parseInlineOptions", "acceptNegative", "parse<PERSON><PERSON><PERSON>", "parseType", "parseEnum", "parseService", "service", "parseMethod", "commentText", "method", "parseExtension", "reference", "parseField", "fnIf", "fnElse", "trailingLine", "parseMapField", "valueType", "extensions", "parseGroup", "lcFirst", "ucFirst", "endsWith", "startsWith", "parseEnumValue", "isOption", "parensValue", "includes", "tokens", "option", "concat", "optionValue", "parseOptionValue", "objectResult", "lastValue", "prevValue", "simpleValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "<PERSON><PERSON><PERSON><PERSON>", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "sisterField", "extendedType", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "lastCommentLine", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "char<PERSON>t", "setComment", "isLeading", "lineEmpty", "leading", "lookback", "commentOffset", "lines", "trim", "text", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "match", "lastIndex", "exec", "repeat", "curr", "isDoc", "isLeadingComment", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "originalThis", "wrapper", "fork", "l<PERSON>im", "typeName", "bake", "o", "safePropBackslashRe", "safePropQuoteRe", "camelCaseRe", "toUpperCase", "decorateEnumIndex", "a", "decorateRoot", "enumerable", "dst", "setProp", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "src", "newError", "CustomError", "captureStackTrace", "writable", "configurable", "pool", "versions", "node", "window", "isFinite", "isset", "isSet", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "oneofProp", "invalid", "genVerifyValue", "messageName", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeStringBuffer", "writeBytesBuffer", "copy", "byteLength"], "mappings": ";;;;;;AAAA,CAAA,SAAAA,IAAA,aAAA,CAAA,SAAAC,EAAAC,EAAAC,GAcA,IAAAC,EAPA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAI,GAGA,OAFAC,GACAN,EAAAK,GAAA,GAAAE,KAAAD,EAAAL,EAAAI,GAAA,CAAAG,QAAA,EAAA,EAAAJ,EAAAE,EAAAA,EAAAE,OAAA,EACAF,EAAAE,OACA,EAEAN,EAAA,EAAA,EAGAC,EAAAM,KAAAC,OAAAP,SAAAA,EAGA,YAAA,OAAAQ,QAAAA,OAAAC,KACAD,OAAA,CAAA,QAAA,SAAAE,GAKA,OAJAA,GAAAA,EAAAC,SACAX,EAAAM,KAAAI,KAAAA,EACAV,EAAAY,UAAA,GAEAZ,CACA,CAAA,EAGA,UAAA,OAAAa,QAAAA,QAAAA,OAAAR,UACAQ,OAAAR,QAAAL,EAEA,EAAA,CAAAc,EAAA,CAAA,SAAAC,EAAAF,EAAAR,GChCAQ,EAAAR,QAmBA,SAAAW,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,CAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,CAAA,IAAAF,UAAAG,CAAA,IACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,EAAA,CAAA,EACAI,EACAD,EAAAC,CAAA,MACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,CAAA,EACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,CAAA,IAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,CAAA,CACA,CAEA,EACA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,CAAA,CAMA,CALA,MAAAU,GACAJ,IACAA,EAAA,CAAA,EACAG,EAAAC,CAAA,EAEA,CACA,CAAA,CACA,C,yBCrCAE,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,GAAA,CAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,EAAA,EAAAD,EAAA,GAAA,MAAAD,EAAAA,EAAAC,IAAAD,KACA,EAAAE,EACA,OAAAC,KAAAC,KAAA,EAAAJ,EAAAV,MAAA,EAAA,EAAAY,CACA,EASA,IAxBA,IAkBAG,EAAAjB,MAAA,EAAA,EAGAkB,EAAAlB,MAAA,GAAA,EAGAmB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,CAAA,GASAR,EAAAS,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,CAAA,IACA,OAAAK,GACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,CAAA,IAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,CAAA,IAAAF,EAAA,GAAAW,GACAD,EAAA,CAEA,CACA,KAAAR,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,EAEA,CAOA,OANAQ,IACAD,EAAAP,CAAA,IAAAF,EAAAO,GACAE,EAAAP,CAAA,IAAA,GACA,IAAAQ,IACAD,EAAAP,CAAA,IAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EAEA,IAAAe,EAAA,mBAUAvB,EAAAwB,OAAA,SAAAvB,EAAAS,EAAAlB,GAIA,IAHA,IAEAqB,EAFAF,EAAAnB,EACAwB,EAAA,EAEAR,EAAA,EAAAA,EAAAP,EAAAV,QAAA,CACA,IAAAkC,EAAAxB,EAAAyB,WAAAlB,CAAA,EAAA,EACA,GAAA,IAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAA3D,GACA,MAAA6D,MAAAJ,CAAA,EACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,IAAAqB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,GAAAqB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAlB,CAAA,KAAA,EAAAqB,IAAA,EAAAY,EACAT,EAAA,CAEA,CACA,CACA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,CAAA,EACA,OAAA/B,EAAAmB,CACA,EAOAX,EAAA4B,KAAA,SAAA3B,GACA,MAAA,mEAAA2B,KAAA3B,CAAA,CACA,C,yBChIA,SAAA4B,EAAAC,EAAAC,GAGA,UAAA,OAAAD,IACAC,EAAAD,EACAA,EAAAhE,IAGA,IAAAkE,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,UAAA,OAAAA,EAAA,CACA,IAAAC,EAAAC,EAAA,EAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,CAAA,EACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,CAAA,EACAS,EAAAtD,MAAAmD,EAAAjD,OAAA,CAAA,EACAqD,EAAAvD,MAAAmD,EAAAjD,MAAA,EACAsD,EAAA,EACAA,EAAAL,EAAAjD,QACAoD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,CAAA,KAGA,OADAF,EAAAE,GAAAV,EACAW,SAAA/C,MAAA,KAAA4C,CAAA,EAAA5C,MAAA,KAAA6C,CAAA,CACA,CACA,OAAAE,SAAAX,CAAA,EAAA,CACA,CAKA,IAFA,IAAAY,EAAA1D,MAAAC,UAAAC,OAAA,CAAA,EACAyD,EAAA,EACAA,EAAAD,EAAAxD,QACAwD,EAAAC,GAAA1D,UAAA,EAAA0D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,CAAA,IACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,MAAAhC,IAAAkC,EAAAA,GAAAD,GACA,IAAA,IAAA,MAAAjC,GAAAf,KAAAkD,MAAAF,CAAA,EACA,IAAA,IAAA,OAAAG,KAAAC,UAAAJ,CAAA,EACA,IAAA,IAAA,MAAAjC,GAAAiC,CACA,CACA,MAAA,GACA,CAAA,EACAJ,IAAAD,EAAAxD,OACA,MAAAoC,MAAA,0BAAA,EAEA,OADAK,EAAAd,KAAAgB,CAAA,EACAD,CACA,CAEA,SAAAG,EAAAqB,GACA,MAAA,aAAAA,GAAA1B,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,GAAA,GAAA,IAAA,SAAAU,EAAAV,KAAA,MAAA,EAAA,KACA,CAGA,OADAW,EAAAG,SAAAA,EACAH,CACA,EAjFAlD,EAAAR,QAAAsD,GAiGAQ,QAAA,CAAA,C,yBCzFA,SAAAqB,IAOAC,KAAAC,EAAA,EACA,EAhBA7E,EAAAR,QAAAmF,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA7C,KAAA,CACAhC,GAAAA,EACAC,IAAAA,GAAAwE,IACA,CAAA,EACAA,IACA,EAQAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAjG,GACA6F,KAAAC,EAAA,QAEA,GAAA1E,IAAApB,GACA6F,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAvD,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,KAAAA,EACA+E,EAAAC,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EAGA,OAAAmD,IACA,EAQAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA5D,EAAA,EACAA,EAAAlB,UAAAC,QACA6E,EAAAlD,KAAA5B,UAAAkB,CAAA,GAAA,EACA,IAAAA,EAAA,EAAAA,EAAAyD,EAAA1E,QACA0E,EAAAzD,GAAAtB,GAAAa,MAAAkE,EAAAzD,CAAA,IAAArB,IAAAiF,CAAA,CACA,CACA,OAAAT,IACA,C,yBC1EA5E,EAAAR,QAAA8F,EAEA,IAAAC,EAAArF,EAAA,CAAA,EAGAsF,EAFAtF,EAAA,CAAA,EAEA,IAAA,EA2BA,SAAAoF,EAAAG,EAAAC,EAAAC,GAOA,OAJAD,EAFA,YAAA,OAAAA,GACAC,EAAAD,EACA,IACAA,GACA,GAEAC,EAIA,CAAAD,EAAAE,KAAAJ,GAAAA,EAAAK,SACAL,EAAAK,SAAAJ,EAAA,SAAA1E,EAAA+E,GACA,OAAA/E,GAAA,aAAA,OAAAgF,eACAT,EAAAM,IAAAH,EAAAC,EAAAC,CAAA,EACA5E,EACA4E,EAAA5E,CAAA,EACA4E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,MAAA,CAAA,CACA,CAAA,EAGAiC,EAAAM,IAAAH,EAAAC,EAAAC,CAAA,EAbAJ,EAAAD,EAAAV,KAAAa,EAAAC,CAAA,CAcA,CAuBAJ,EAAAM,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAnH,GAKA,GAAA,IAAA6G,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,MAAA,CAAA,EAIA,GAAAT,EAAAM,OAAA,CAEA,GAAA,EAAArE,EADAiE,EAAAQ,UAGA,IAAA,IADAzE,EAAA,GACAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA7F,OAAA,EAAAiB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,CAAA,CAAA,EAEA,OAAAkE,EAAA,KAAA,aAAA,OAAAW,WAAA,IAAAA,WAAA3E,CAAA,EAAAA,CAAA,CACA,CACA,OAAAgE,EAAA,KAAAC,EAAAS,YAAA,CACA,EAEAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,oCAAA,EACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,CAAA,EACAG,EAAAc,KAAA,CACA,C,gCC3BA,SAAAC,EAAAnH,GAsDA,SAAAoH,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAAH,EAAA,EAAA,EAAA,EAIAD,EADA,KADAC,EADAG,EACA,CAAAH,EACAA,GACA,EAAA,EAAAA,EAAA,EAAA,WACAI,MAAAJ,CAAA,EACA,WACA,qBAAAA,GACAG,GAAA,GAAA,cAAA,EACAH,EAAA,uBACAG,GAAA,GAAA5F,KAAA8F,MAAAL,EAAA,oBAAA,KAAA,GAIAG,GAAA,GAAA,KAFAG,EAAA/F,KAAAkD,MAAAlD,KAAAmC,IAAAsD,CAAA,EAAAzF,KAAAgG,GAAA,IAEA,GADA,QAAAhG,KAAA8F,MAAAL,EAAAzF,KAAAiG,IAAA,EAAA,CAAAF,CAAA,EAAA,OAAA,KACA,EAVAL,EAAAC,CAAA,CAYA,CAKA,SAAAO,EAAAC,EAAAT,EAAAC,GACAS,EAAAD,EAAAT,EAAAC,CAAA,EACAC,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,GAAA,QACA,OAAA,KAAAN,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,qBAAAH,EAAAS,EACAT,EAAA5F,KAAAiG,IAAA,EAAAF,EAAA,GAAA,GAAA,QAAAM,EACA,CA/EA,SAAAG,EAAAf,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAEA,SAAAC,EAAAlB,EAAAC,EAAAC,GACAc,EAAA,GAAAhB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAOA,SAAAE,EAAAlB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,EACA,CAEA,SAAAI,EAAAnB,EAAAC,GAKA,OAJAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAc,EAAA,EACA,CAzCA,IAEAA,EACAC,EA4FAI,EACAJ,EACAK,EA+DA,SAAAC,EAAAxB,EAAAyB,EAAAC,EAAAzB,EAAAC,EAAAC,GACA,IAaAU,EAbAT,EAAAH,EAAA,EAAA,EAAA,EAGA,KADAA,EADAG,EACA,CAAAH,EACAA,IACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,EAAA,EAAA,EAAAC,EAAA,EAAA,WAAAC,EAAAC,EAAAuB,CAAA,GACArB,MAAAJ,CAAA,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,EAAA,WAAAE,EAAAC,EAAAuB,CAAA,GACA,sBAAAzB,GACAD,EAAA,EAAAE,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAA,cAAA,EAAAF,EAAAC,EAAAuB,CAAA,GAGAzB,EAAA,wBAEAD,GADAa,EAAAZ,EAAA,UACA,EAAAC,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAAS,EAAA,cAAA,EAAAX,EAAAC,EAAAuB,CAAA,IAMA1B,EAAA,kBADAa,EAAAZ,EAAAzF,KAAAiG,IAAA,EAAA,EADAF,EADA,QADAA,EAAA/F,KAAAkD,MAAAlD,KAAAmC,IAAAsD,CAAA,EAAAzF,KAAAgG,GAAA,GAEA,KACAD,EAAA,KACA,EAAAL,EAAAC,EAAAsB,CAAA,EACAzB,GAAAI,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAAX,EAAAC,EAAAuB,CAAA,EAGA,CAKA,SAAAC,EAAAhB,EAAAc,EAAAC,EAAAxB,EAAAC,GACAyB,EAAAjB,EAAAT,EAAAC,EAAAsB,CAAA,EACAI,EAAAlB,EAAAT,EAAAC,EAAAuB,CAAA,EACAtB,EAAA,GAAAyB,GAAA,IAAA,EACAtB,EAAAsB,IAAA,GAAA,KACAhB,EAAA,YAAA,QAAAgB,GAAAD,EACA,OAAA,MAAArB,EACAM,EACAC,IACAC,EAAAA,EAAAX,EACA,GAAAG,EACA,OAAAH,EAAAS,EACAT,EAAA5F,KAAAiG,IAAA,EAAAF,EAAA,IAAA,GAAAM,EAAA,iBACA,CA3GA,SAAAiB,EAAA7B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAEA,SAAAa,EAAA9B,EAAAC,EAAAC,GACAmB,EAAA,GAAArB,EACAC,EAAAC,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,GACAhB,EAAAC,EAAA,GAAAe,EAAA,EACA,CAOA,SAAAc,EAAA9B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,EACA,CAEA,SAAAW,EAAA/B,EAAAC,GASA,OARAe,EAAA,GAAAhB,EAAAC,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAe,EAAA,GAAAhB,EAAAC,EAAA,GACAmB,EAAA,EACA,CA+DA,MArNA,aAAA,OAAAY,cAEAjB,EAAA,IAAAiB,aAAA,CAAA,CAAA,EAAA,EACAhB,EAAA,IAAAzB,WAAAwB,EAAAnG,MAAA,EACAyG,EAAA,MAAAL,EAAA,GAmBAvI,EAAAwJ,aAAAZ,EAAAP,EAAAG,EAEAxI,EAAAyJ,aAAAb,EAAAJ,EAAAH,EAmBArI,EAAA0J,YAAAd,EAAAH,EAAAC,EAEA1I,EAAA2J,YAAAf,EAAAF,EAAAD,IAwBAzI,EAAAwJ,aAAApC,EAAAwC,KAAA,KAAAC,CAAA,EACA7J,EAAAyJ,aAAArC,EAAAwC,KAAA,KAAAE,CAAA,EAgBA9J,EAAA0J,YAAA3B,EAAA6B,KAAA,KAAAG,CAAA,EACA/J,EAAA2J,YAAA5B,EAAA6B,KAAA,KAAAI,CAAA,GAKA,aAAA,OAAAC,cAEAtB,EAAA,IAAAsB,aAAA,CAAA,CAAA,EAAA,EACA1B,EAAA,IAAAzB,WAAA6B,EAAAxG,MAAA,EACAyG,EAAA,MAAAL,EAAA,GA2BAvI,EAAAkK,cAAAtB,EAAAO,EAAAC,EAEApJ,EAAAmK,cAAAvB,EAAAQ,EAAAD,EA2BAnJ,EAAAoK,aAAAxB,EAAAS,EAAAC,EAEAtJ,EAAAqK,aAAAzB,EAAAU,EAAAD,IAmCArJ,EAAAkK,cAAArB,EAAAe,KAAA,KAAAC,EAAA,EAAA,CAAA,EACA7J,EAAAmK,cAAAtB,EAAAe,KAAA,KAAAE,EAAA,EAAA,CAAA,EAiBA9J,EAAAoK,aAAApB,EAAAY,KAAA,KAAAG,EAAA,EAAA,CAAA,EACA/J,EAAAqK,aAAArB,EAAAY,KAAA,KAAAI,EAAA,EAAA,CAAA,GAIAhK,CACA,CAIA,SAAA6J,EAAAvC,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CAEA,SAAAwC,EAAAxC,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,CACA,CAEA,SAAAyC,EAAAxC,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,CACA,CAEA,SAAAwC,EAAAzC,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,CACA,CA5UAhH,EAAAR,QAAAmH,EAAAA,CAAA,C,yBCOA,SAAAmD,EAAAC,GACA,IACA,IAAAC,EAAAC,KAAA,SAAA,EAAAF,CAAA,EACA,GAAAC,IAAAA,EAAAxJ,QAAAkD,OAAAC,KAAAqG,CAAA,EAAAxJ,QACA,OAAAwJ,CACA,CAAA,MAAAE,IACA,OAAA,IACA,CAfAlK,EAAAR,QAAAsK,C,yBCMA,IAEAK,EAMAC,EAAAD,WAAA,SAAAC,GACA,MAAA,eAAAvH,KAAAuH,CAAA,CACA,EAEAC,EAMAD,EAAAC,UAAA,SAAAD,GAGA,IAAArI,GAFAqI,EAAAA,EAAAlG,QAAA,MAAA,GAAA,EACAA,QAAA,UAAA,GAAA,GACAoG,MAAA,GAAA,EACAC,EAAAJ,EAAAC,CAAA,EACAI,EAAA,GACAD,IACAC,EAAAzI,EAAA0I,MAAA,EAAA,KACA,IAAA,IAAAhJ,EAAA,EAAAA,EAAAM,EAAAvB,QACA,OAAAuB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAoD,OAAA,EAAA1D,EAAA,CAAA,EACA8I,EACAxI,EAAAoD,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EACA,MAAAM,EAAAN,GACAM,EAAAoD,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EAEA,OAAA+I,EAAAzI,EAAAQ,KAAA,GAAA,CACA,EASA6H,EAAAvJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,CAAA,GACAR,CAAAA,EAAAQ,CAAA,IAIAD,GADAA,EADAE,EAEAF,EADAL,EAAAK,CAAA,GACAxG,QAAA,iBAAA,EAAA,GAAA1D,OAAA6J,EAAAK,EAAA,IAAAC,CAAA,EAHAA,CAIA,C,yBC/DA3K,EAAAR,QA6BA,SAAAqL,EAAAvI,EAAAwI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,CAAA,EACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,CAAA,EACAtK,EAAA,GAEAsG,EAAAzE,EAAA/C,KAAA0L,EAAAxK,EAAAA,GAAAqK,CAAA,EAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACAsG,CACA,CACA,C,0BCjCAmE,EAAA1K,OAAA,SAAAU,GAGA,IAFA,IACAwB,EADAyI,EAAA,EAEA1J,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACAiB,EAAAxB,EAAAyB,WAAAlB,CAAA,GACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAxB,EAAAyB,WAAAlB,EAAA,CAAA,IACA,EAAAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,CACA,EASAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,CAAA,KACA,IACAI,EAAAP,CAAA,IAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,CAAA,IACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,IAAA,GAAAD,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,KAAA,MACAI,EAAAP,CAAA,IAAA,OAAAK,GAAA,IACAE,EAAAP,CAAA,IAAA,OAAA,KAAAK,IAEAE,EAAAP,CAAA,KAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,CAAA,MAAA,EAAA,GAAAD,EAAAC,CAAA,IACA,KAAAH,KACAM,EAAAA,GAAA,IAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,CAAA,CAAA,EACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CAAA,EACAM,EAAAQ,KAAA,EAAA,GAEAH,OAAAC,aAAArB,MAAAoB,OAAAJ,EAAAM,MAAA,EAAAb,CAAA,CAAA,CACA,EASAyJ,EAAAG,MAAA,SAAAnK,EAAAS,EAAAlB,GAIA,IAHA,IACA6K,EACAC,EAFA3J,EAAAnB,EAGAgB,EAAA,EAAAA,EAAAP,EAAAV,OAAA,EAAAiB,GACA6J,EAAApK,EAAAyB,WAAAlB,CAAA,GACA,IACAE,EAAAlB,CAAA,IAAA6K,GACAA,EAAA,KACA3J,EAAAlB,CAAA,IAAA6K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAArK,EAAAyB,WAAAlB,EAAA,CAAA,KAEA,EAAAA,EACAE,EAAAlB,CAAA,KAFA6K,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KAEA,GAAA,IACA5J,EAAAlB,CAAA,IAAA6K,GAAA,GAAA,GAAA,KAIA3J,EAAAlB,CAAA,IAAA6K,GAAA,GAAA,IAHA3J,EAAAlB,CAAA,IAAA6K,GAAA,EAAA,GAAA,KANA3J,EAAAlB,CAAA,IAAA,GAAA6K,EAAA,KAcA,OAAA7K,EAAAmB,CACA,C,0BCvGA5B,EAAAR,QAAAgM,EAEA,IAAAC,EAAA,QAsBA,SAAAD,EAAAnM,EAAAqM,GACAD,EAAA5I,KAAAxD,CAAA,IACAA,EAAA,mBAAAA,EAAA,SACAqM,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAxM,SAAA,CAAAwM,OAAAD,CAAA,CAAA,CAAA,CAAA,CAAA,GAEAF,EAAAnM,GAAAqM,CACA,CAWAF,EAAA,MAAA,CAUAK,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,CACA,EACA5H,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAIAT,EAAA,WAAA,CAUAU,SAAAC,EAAA,CACAL,OAAA,CACAM,QAAA,CACAJ,KAAA,QACAC,GAAA,CACA,EACAI,MAAA,CACAL,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,YAAA,CAUAc,UAAAH,CACA,CAAA,EAEAX,EAAA,QAAA,CAOAe,MAAA,CACAT,OAAA,EACA,CACA,CAAA,EAEAN,EAAA,SAAA,CASAgB,OAAA,CACAV,OAAA,CACAA,OAAA,CACAW,QAAA,SACAT,KAAA,QACAC,GAAA,CACA,CACA,CACA,EAeAS,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,YAEA,CACA,EACAf,OAAA,CACAgB,UAAA,CACAd,KAAA,YACAC,GAAA,CACA,EACAc,YAAA,CACAf,KAAA,SACAC,GAAA,CACA,EACAe,YAAA,CACAhB,KAAA,SACAC,GAAA,CACA,EACAgB,UAAA,CACAjB,KAAA,OACAC,GAAA,CACA,EACAiB,YAAA,CACAlB,KAAA,SACAC,GAAA,CACA,EACAkB,UAAA,CACAnB,KAAA,YACAC,GAAA,CACA,CACA,CACA,EAEAmB,UAAA,CACAC,OAAA,CACAC,WAAA,CACA,CACA,EASAC,UAAA,CACAzB,OAAA,CACAuB,OAAA,CACAG,KAAA,WACAxB,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,WAAA,CASAiC,YAAA,CACA3B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASAyB,WAAA,CACA5B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA0B,WAAA,CACA7B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA2B,YAAA,CACA9B,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASA4B,WAAA,CACA/B,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,EASA6B,YAAA,CACAhC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASA8B,UAAA,CACAjC,OAAA,CACAzH,MAAA,CACA2H,KAAA,OACAC,GAAA,CACA,CACA,CACA,EASA+B,YAAA,CACAlC,OAAA,CACAzH,MAAA,CACA2H,KAAA,SACAC,GAAA,CACA,CACA,CACA,EASAgC,WAAA,CACAnC,OAAA,CACAzH,MAAA,CACA2H,KAAA,QACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAEAT,EAAA,aAAA,CASA0C,UAAA,CACApC,OAAA,CACAqC,MAAA,CACAX,KAAA,WACAxB,KAAA,SACAC,GAAA,CACA,CACA,CACA,CACA,CAAA,EAiBAT,EAAA4C,IAAA,SAAAC,GACA,OAAA7C,EAAA6C,IAAA,IACA,C,0BCzYA,IAEAC,EAAApO,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAWA,SAAAqO,EAAAC,EAAAC,EAAAC,EAAAC,GACA,IAAAC,EAAA,CAAA,EAEA,GAAAH,EAAAI,aACA,GAAAJ,EAAAI,wBAAAP,EAAA,CAAAE,EACA,eAAAG,CAAA,EACA,IAAA,IAAAtB,EAAAoB,EAAAI,aAAAxB,OAAA1J,EAAAD,OAAAC,KAAA0J,CAAA,EAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EAEA4L,EAAA1J,EAAAlC,MAAAgN,EAAAK,aAAAF,IAAAJ,EACA,UAAA,EACA,4CAAAG,EAAAA,EAAAA,CAAA,EACAF,EAAAM,UAAAP,EAEA,OAAA,EACAI,EAAA,CAAA,GAEAJ,EACA,UAAA7K,EAAAlC,EAAA,EACA,WAAA4L,EAAA1J,EAAAlC,GAAA,EACA,SAAAkN,EAAAtB,EAAA1J,EAAAlC,GAAA,EACA,OAAA,EACA+M,EACA,GAAA,CACA,MAAAA,EACA,4BAAAG,CAAA,EACA,sBAAAF,EAAAO,SAAA,mBAAA,EACA,gCAAAL,EAAAD,EAAAC,CAAA,MACA,CACA,IAAAM,EAAA,CAAA,EACA,OAAAR,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,kBAAAG,EAAAA,CAAA,EACA,MACA,IAAA,SACA,IAAA,UAAAH,EACA,cAAAG,EAAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAH,EACA,YAAAG,EAAAA,CAAA,EACA,MACA,IAAA,SACAM,EAAA,CAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,eAAA,EACA,6CAAAG,EAAAA,EAAAM,CAAA,EACA,iCAAAN,CAAA,EACA,uBAAAA,EAAAA,CAAA,EACA,iCAAAA,CAAA,EACA,UAAAA,EAAAA,CAAA,EACA,iCAAAA,CAAA,EACA,+DAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,EAAA,EACA,MACA,IAAA,QAAAT,EACA,4BAAAG,CAAA,EACA,wEAAAA,EAAAA,EAAAA,CAAA,EACA,2BAAAA,CAAA,EACA,UAAAA,EAAAA,CAAA,EACA,MACA,IAAA,SAAAH,EACA,kBAAAG,EAAAA,CAAA,EACA,MACA,IAAA,OAAAH,EACA,mBAAAG,EAAAA,CAAA,CAKA,CACA,CACA,OAAAH,CAEA,CAiEA,SAAAU,EAAAV,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAI,aACAJ,EAAAI,wBAAAP,EAAAE,EACA,yFAAAG,EAAAD,EAAAC,EAAAA,EAAAD,EAAAC,EAAAA,CAAA,EACAH,EACA,gCAAAG,EAAAD,EAAAC,CAAA,MACA,CACA,IAAAM,EAAA,CAAA,EACA,OAAAR,EAAAzC,MACA,IAAA,SACA,IAAA,QAAAwC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,CAAA,EACA,MACA,IAAA,SACAM,EAAA,CAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,4BAAAG,CAAA,EACA,uCAAAA,EAAAA,EAAAA,CAAA,EACA,MAAA,EACA,4IAAAA,EAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,GAAAN,CAAA,EACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,CAAA,EACA,MACA,QAAAH,EACA,UAAAG,EAAAA,CAAA,CAEA,CACA,CACA,OAAAH,CAEA,CA9FAW,EAAAC,WAAA,SAAAC,GAEA,IAAAvD,EAAAuD,EAAAC,YACAd,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,KAAA,aAAA,EACA,4BAAA,EACA,UAAA,EACA,GAAA,CAAAyM,EAAAtL,OAAA,OAAAgO,EACA,sBAAA,EACAA,EACA,qBAAA,EACA,IAAA,IAAA/M,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,QAAA,EACA8N,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EAGAoP,EAAAe,KAAAhB,EACA,WAAAG,CAAA,EACA,4BAAAA,CAAA,EACA,sBAAAF,EAAAO,SAAA,mBAAA,EACA,SAAAL,CAAA,EACA,oDAAAA,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,SAAA,EACA,GAAA,EACA,GAAA,GAGAF,EAAAM,UAAAP,EACA,WAAAG,CAAA,EACA,0BAAAA,CAAA,EACA,sBAAAF,EAAAO,SAAA,kBAAA,EACA,SAAAL,CAAA,EACA,iCAAAA,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,EAAA,KAAA,EACA,GAAA,EACA,GAAA,IAIAF,EAAAI,wBAAAP,GAAAE,EACA,iBAAAG,CAAA,EACAJ,EAAAC,EAAAC,EAAAhN,EAAAkN,CAAA,EACAF,EAAAI,wBAAAP,GAAAE,EACA,GAAA,EAEA,CAAA,OAAAA,EACA,UAAA,CAEA,EAsDAW,EAAAM,SAAA,SAAAJ,GAEA,IAAAvD,EAAAuD,EAAAC,YAAAhN,MAAA,EAAAoN,KAAAjQ,EAAAkQ,iBAAA,EACA,GAAA,CAAA7D,EAAAtL,OACA,OAAAf,EAAAqD,QAAA,EAAA,WAAA,EAUA,IATA,IAAA0L,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAuM,EAAAhQ,KAAA,WAAA,EACA,QAAA,EACA,MAAA,EACA,UAAA,EAEAuQ,EAAA,GACAC,EAAA,GACAC,EAAA,GACArO,EAAA,EACAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EACAqK,EAAArK,GAAAsO,SACAjE,EAAArK,GAAAZ,QAAA,EAAAkO,SAAAa,EACA9D,EAAArK,GAAA+N,IAAAK,EACAC,GAAA3N,KAAA2J,EAAArK,EAAA,EAEA,GAAAmO,EAAApP,OAAA,CAEA,IAFAgO,EACA,2BAAA,EACA/M,EAAA,EAAAA,EAAAmO,EAAApP,OAAA,EAAAiB,EAAA+M,EACA,SAAA/O,EAAA8P,SAAAK,EAAAnO,GAAApC,IAAA,CAAA,EACAmP,EACA,GAAA,CACA,CAEA,GAAAqB,EAAArP,OAAA,CAEA,IAFAgO,EACA,4BAAA,EACA/M,EAAA,EAAAA,EAAAoO,EAAArP,OAAA,EAAAiB,EAAA+M,EACA,SAAA/O,EAAA8P,SAAAM,EAAApO,GAAApC,IAAA,CAAA,EACAmP,EACA,GAAA,CACA,CAEA,GAAAsB,EAAAtP,OAAA,CAEA,IAFAgO,EACA,iBAAA,EACA/M,EAAA,EAAAA,EAAAqO,EAAAtP,OAAA,EAAAiB,EAAA,CACA,IAWAuO,EAXAvB,EAAAqB,EAAArO,GACAkN,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EACAoP,EAAAI,wBAAAP,EAAAE,EACA,6BAAAG,EAAAF,EAAAI,aAAAoB,WAAAxB,EAAAK,aAAAL,EAAAK,WAAA,EACAL,EAAAyB,KAAA1B,EACA,gBAAA,EACA,gCAAAC,EAAAK,YAAAqB,IAAA1B,EAAAK,YAAAsB,KAAA3B,EAAAK,YAAAuB,QAAA,EACA,oEAAA1B,CAAA,EACA,OAAA,EACA,6BAAAA,EAAAF,EAAAK,YAAAzL,SAAA,EAAAoL,EAAAK,YAAAwB,SAAA,CAAA,EACA7B,EAAA8B,OACAP,EAAA,IAAA1P,MAAAwE,UAAAxC,MAAA/C,KAAAkP,EAAAK,WAAA,EAAAvM,KAAA,GAAA,EAAA,IACAiM,EACA,6BAAAG,EAAAvM,OAAAC,aAAArB,MAAAoB,OAAAqM,EAAAK,WAAA,CAAA,EACA,OAAA,EACA,SAAAH,EAAAqB,CAAA,EACA,6CAAArB,EAAAA,CAAA,EACA,GAAA,GACAH,EACA,SAAAG,EAAAF,EAAAK,WAAA,CACA,CAAAN,EACA,GAAA,CACA,CAEA,IADA,IAAAgC,EAAA,CAAA,EACA/O,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GACAf,EAAA2O,EAAAoB,EAAAC,QAAAjC,CAAA,EACAE,EAAAlP,EAAA8P,SAAAd,EAAApP,IAAA,EACAoP,EAAAe,KACAgB,IAAAA,EAAA,CAAA,EAAAhC,EACA,SAAA,GACAA,EACA,0CAAAG,EAAAA,CAAA,EACA,SAAAA,CAAA,EACA,gCAAA,EACAO,EAAAV,EAAAC,EAAA/N,EAAAiO,EAAA,UAAA,EACA,GAAA,GACAF,EAAAM,UAAAP,EACA,uBAAAG,EAAAA,CAAA,EACA,SAAAA,CAAA,EACA,iCAAAA,CAAA,EACAO,EAAAV,EAAAC,EAAA/N,EAAAiO,EAAA,KAAA,EACA,GAAA,IACAH,EACA,uCAAAG,EAAAF,EAAApP,IAAA,EACA6P,EAAAV,EAAAC,EAAA/N,EAAAiO,CAAA,EACAF,EAAAsB,QAAAvB,EACA,cAAA,EACA,SAAA/O,EAAA8P,SAAAd,EAAAsB,OAAA1Q,IAAA,EAAAoP,EAAApP,IAAA,GAEAmP,EACA,GAAA,CACA,CACA,OAAAA,EACA,UAAA,CAEA,C,qCC3SAxO,EAAAR,QAeA,SAAA6P,GAaA,IAXA,IAAAb,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,IAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,4BAAA,EACA,oBAAA,EACA,qDAAAgQ,EAAAC,YAAAqB,OAAA,SAAAlC,GAAA,OAAAA,EAAAe,GAAA,CAAA,EAAAhP,OAAA,WAAA,GAAA,EACA,iBAAA,EACA,kBAAA,EACA,WAAA,EACA,OAAA,EACA,gBAAA,EAEAiB,EAAA,EACAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAAY,EAAAoB,EAAAhP,GAAAZ,QAAA,EACAmL,EAAAyC,EAAAI,wBAAAP,EAAA,QAAAG,EAAAzC,KACA4E,EAAA,IAAAnR,EAAA8P,SAAAd,EAAApP,IAAA,EAAAmP,EACA,aAAAC,EAAAxC,EAAA,EAGAwC,EAAAe,KAAAhB,EACA,4BAAAoC,CAAA,EACA,QAAAA,CAAA,EACA,2BAAA,EAEAC,EAAAC,SAAArC,EAAAhC,WAAA1N,GAAAyP,EACA,OAAAqC,EAAAC,SAAArC,EAAAhC,QAAA,EACA+B,EACA,QAAA,EAEAqC,EAAAC,SAAA9E,KAAAjN,GAAAyP,EACA,WAAAqC,EAAAC,SAAA9E,EAAA,EACAwC,EACA,YAAA,EAEAA,EACA,kBAAA,EACA,qBAAA,EACA,mBAAA,EACA,0BAAAC,EAAAhC,OAAA,EACA,SAAA,EAEAoE,EAAAE,MAAA/E,KAAAjN,GAAAyP,EACA,uCAAA/M,CAAA,EACA+M,EACA,eAAAxC,CAAA,EAEAwC,EACA,OAAA,EACA,UAAA,EACA,oBAAA,EACA,OAAA,EACA,GAAA,EACA,GAAA,EAEAqC,EAAAX,KAAAzB,EAAAhC,WAAA1N,GAAAyP,EACA,qDAAAoC,CAAA,EACApC,EACA,cAAAoC,CAAA,GAGAnC,EAAAM,UAAAP,EAEA,uBAAAoC,EAAAA,CAAA,EACA,QAAAA,CAAA,EAGAC,EAAAG,OAAAhF,KAAAjN,IAAAyP,EACA,gBAAA,EACA,yBAAA,EACA,iBAAA,EACA,kBAAAoC,EAAA5E,CAAA,EACA,OAAA,EAGA6E,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAwC,UACA,oDACA,0CAAAL,EAAAnP,CAAA,EACA+M,EACA,kBAAAoC,EAAA5E,CAAA,GAGA6E,EAAAE,MAAA/E,KAAAjN,GAAAyP,EAAAC,EAAAwC,UACA,8CACA,oCAAAL,EAAAnP,CAAA,EACA+M,EACA,YAAAoC,EAAA5E,CAAA,EACAwC,EACA,OAAA,EACA,GAAA,CAEA,CASA,IATAA,EACA,UAAA,EACA,iBAAA,EACA,OAAA,EAEA,GAAA,EACA,GAAA,EAGA/M,EAAA,EAAAA,EAAA4N,EAAAoB,EAAAjQ,OAAA,EAAAiB,EAAA,CACA,IAAAyP,EAAA7B,EAAAoB,EAAAhP,GACAyP,EAAAC,UAAA3C,EACA,4BAAA0C,EAAA7R,IAAA,EACA,4CAhHA,qBAgHA6R,EAhHA7R,KAAA,GAgHA,CACA,CAEA,OAAAmP,EACA,UAAA,CAEA,EA3HA,IAAAF,EAAApO,EAAA,EAAA,EACA2Q,EAAA3Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,C,2CCJAF,EAAAR,QA0BA,SAAA6P,GAWA,IATA,IAIAuB,EAJApC,EAAA/O,EAAAqD,QAAA,CAAA,IAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,QAAA,EACA,mBAAA,EAKAyM,EAAAuD,EAAAC,YAAAhN,MAAA,EAAAoN,KAAAjQ,EAAAkQ,iBAAA,EAEAlO,EAAA,EAAAA,EAAAqK,EAAAtL,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA3C,EAAArK,GAAAZ,QAAA,EACAH,EAAA2O,EAAAoB,EAAAC,QAAAjC,CAAA,EACAzC,EAAAyC,EAAAI,wBAAAP,EAAA,QAAAG,EAAAzC,KACAoF,EAAAP,EAAAE,MAAA/E,GACA4E,EAAA,IAAAnR,EAAA8P,SAAAd,EAAApP,IAAA,EAGAoP,EAAAe,KACAhB,EACA,kDAAAoC,EAAAnC,EAAApP,IAAA,EACA,mDAAAuR,CAAA,EACA,4CAAAnC,EAAAxC,IAAA,EAAA,KAAA,EAAA,EAAA4E,EAAAQ,OAAA5C,EAAAhC,SAAAgC,EAAAhC,OAAA,EACA2E,IAAArS,GAAAyP,EACA,oEAAA9N,EAAAkQ,CAAA,EACApC,EACA,qCAAA,GAAA4C,EAAApF,EAAA4E,CAAA,EACApC,EACA,GAAA,EACA,GAAA,GAGAC,EAAAM,UAAAP,EACA,2BAAAoC,EAAAA,CAAA,EAGAnC,EAAAuC,QAAAH,EAAAG,OAAAhF,KAAAjN,GAAAyP,EAEA,uBAAAC,EAAAxC,IAAA,EAAA,KAAA,CAAA,EACA,+BAAA2E,CAAA,EACA,cAAA5E,EAAA4E,CAAA,EACA,YAAA,GAGApC,EAEA,+BAAAoC,CAAA,EACAQ,IAAArS,GACAuS,EAAA9C,EAAAC,EAAA/N,EAAAkQ,EAAA,KAAA,EACApC,EACA,0BAAAC,EAAAxC,IAAA,EAAAmF,KAAA,EAAApF,EAAA4E,CAAA,GAEApC,EACA,GAAA,IAIAC,EAAA8C,UAAA/C,EACA,iDAAAoC,EAAAnC,EAAApP,IAAA,EAEA+R,IAAArS,GACAuS,EAAA9C,EAAAC,EAAA/N,EAAAkQ,CAAA,EACApC,EACA,uBAAAC,EAAAxC,IAAA,EAAAmF,KAAA,EAAApF,EAAA4E,CAAA,EAGA,CAEA,OAAApC,EACA,UAAA,CAEA,EAhGA,IAAAF,EAAApO,EAAA,EAAA,EACA2Q,EAAA3Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAWA,SAAAoR,EAAA9C,EAAAC,EAAAC,EAAAkC,GACAnC,EAAAwC,UACAzC,EAAA,+CAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,GAAAwC,EAAAxC,IAAA,EAAA,KAAA,CAAA,EACAuC,EAAA,oDAAAE,EAAAkC,GAAAnC,EAAAxC,IAAA,EAAA,KAAA,CAAA,CACA,C,2CCnBAjM,EAAAR,QAAA8O,EAGA,IAAAkD,EAAAtR,EAAA,EAAA,EAGAuR,KAFAnD,EAAAxJ,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAArD,GAAAsD,UAAA,OAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAcA,SAAAoO,EAAAjP,EAAAgO,EAAA3H,EAAAmM,EAAAC,EAAAC,GAGA,GAFAP,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAEA2H,GAAA,UAAA,OAAAA,EACA,MAAA2E,UAAA,0BAAA,EAgDA,GA1CApN,KAAAqL,WAAA,GAMArL,KAAAyI,OAAA3J,OAAAgO,OAAA9M,KAAAqL,UAAA,EAMArL,KAAAiN,QAAAA,EAMAjN,KAAAkN,SAAAA,GAAA,GAMAlN,KAAAmN,cAAAA,EAMAnN,KAAAqN,EAAA,GAMArN,KAAAsN,SAAAnT,GAMAsO,EACA,IAAA,IAAA1J,EAAAD,OAAAC,KAAA0J,CAAA,EAAA5L,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACA,UAAA,OAAA4L,EAAA1J,EAAAlC,MACAmD,KAAAqL,WAAArL,KAAAyI,OAAA1J,EAAAlC,IAAA4L,EAAA1J,EAAAlC,KAAAkC,EAAAlC,GACA,CAKA6M,EAAAxJ,UAAAqN,EAAA,SAAAC,GASA,OARAA,EAAAxN,KAAAyN,GAAAD,EACAZ,EAAA1M,UAAAqN,EAAA5S,KAAAqF,KAAAwN,CAAA,EAEA1O,OAAAC,KAAAiB,KAAAyI,MAAA,EAAAiF,QAAAC,IACA,IAAAC,EAAA9O,OAAA+O,OAAA,GAAA7N,KAAA8N,CAAA,EACA9N,KAAAqN,EAAAM,GAAA7O,OAAA+O,OAAAD,EAAA5N,KAAAmN,eAAAnN,KAAAmN,cAAAQ,IAAA3N,KAAAmN,cAAAQ,GAAAI,QAAA,CACA,CAAA,EAEA/N,IACA,EAgBA0J,EAAAsE,SAAA,SAAAvT,EAAAqM,GACAmH,EAAA,IAAAvE,EAAAjP,EAAAqM,EAAA2B,OAAA3B,EAAAhG,QAAAgG,EAAAmG,QAAAnG,EAAAoG,QAAA,EAKA,OAJAe,EAAAX,SAAAxG,EAAAwG,SACAxG,EAAA0G,UACAS,EAAAR,EAAA3G,EAAA0G,SACAS,EAAAC,EAAA,SACAD,CACA,EAOAvE,EAAAxJ,UAAAiO,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAuO,EAAA,EACA,UAAAvO,KAAAc,QACA,gBAAAd,KAAAmN,cACA,SAAAnN,KAAAyI,OACA,WAAAzI,KAAAsN,UAAAtN,KAAAsN,SAAA1R,OAAAoE,KAAAsN,SAAAnT,GACA,UAAAkU,EAAArO,KAAAiN,QAAA9S,GACA,WAAAkU,EAAArO,KAAAkN,SAAA/S,GACA,CACA,EAYAuP,EAAAxJ,UAAAsO,IAAA,SAAA/T,EAAA4M,EAAA4F,EAAAnM,GAGA,GAAA,CAAAjG,EAAA4T,SAAAhU,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,GAAA,CAAAvS,EAAA6T,UAAArH,CAAA,EACA,MAAA+F,UAAA,uBAAA,EAEA,GAAApN,KAAAyI,OAAAhO,KAAAN,GACA,MAAA6D,MAAA,mBAAAvD,EAAA,QAAAuF,IAAA,EAEA,GAAAA,KAAA2O,aAAAtH,CAAA,EACA,MAAArJ,MAAA,MAAAqJ,EAAA,mBAAArH,IAAA,EAEA,GAAAA,KAAA4O,eAAAnU,CAAA,EACA,MAAAuD,MAAA,SAAAvD,EAAA,oBAAAuF,IAAA,EAEA,GAAAA,KAAAqL,WAAAhE,KAAAlN,GAAA,CACA,GAAA6F,CAAAA,KAAAc,SAAAd,CAAAA,KAAAc,QAAA+N,YACA,MAAA7Q,MAAA,gBAAAqJ,EAAA,OAAArH,IAAA,EACAA,KAAAyI,OAAAhO,GAAA4M,CACA,MACArH,KAAAqL,WAAArL,KAAAyI,OAAAhO,GAAA4M,GAAA5M,EASA,OAPAqG,IACAd,KAAAmN,gBAAAhT,KACA6F,KAAAmN,cAAA,IACAnN,KAAAmN,cAAA1S,GAAAqG,GAAA,MAGAd,KAAAkN,SAAAzS,GAAAwS,GAAA,KACAjN,IACA,EASA0J,EAAAxJ,UAAA4O,OAAA,SAAArU,GAEA,GAAA,CAAAI,EAAA4T,SAAAhU,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,IAAAlL,EAAAlC,KAAAyI,OAAAhO,GACA,GAAA,MAAAyH,EACA,MAAAlE,MAAA,SAAAvD,EAAA,uBAAAuF,IAAA,EAQA,OANA,OAAAA,KAAAqL,WAAAnJ,GACA,OAAAlC,KAAAyI,OAAAhO,GACA,OAAAuF,KAAAkN,SAAAzS,GACAuF,KAAAmN,eACA,OAAAnN,KAAAmN,cAAA1S,GAEAuF,IACA,EAOA0J,EAAAxJ,UAAAyO,aAAA,SAAAtH,GACA,OAAAwF,EAAA8B,aAAA3O,KAAAsN,SAAAjG,CAAA,CACA,EAOAqC,EAAAxJ,UAAA0O,eAAA,SAAAnU,GACA,OAAAoS,EAAA+B,eAAA5O,KAAAsN,SAAA7S,CAAA,CACA,C,2CC7NAW,EAAAR,QAAAmU,EAGA,IAOAC,EAPApC,EAAAtR,EAAA,EAAA,EAGAoO,KAFAqF,EAAA7O,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAgC,GAAA/B,UAAA,QAEA1R,EAAA,EAAA,GACA2Q,EAAA3Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAIA2T,EAAA,+BA6CA,SAAAF,EAAAtU,EAAA4M,EAAAD,EAAAwB,EAAAsG,EAAApO,EAAAmM,GAcA,GAZApS,EAAAsU,SAAAvG,CAAA,GACAqE,EAAAiC,EACApO,EAAA8H,EACAA,EAAAsG,EAAA/U,IACAU,EAAAsU,SAAAD,CAAA,IACAjC,EAAAnM,EACAA,EAAAoO,EACAA,EAAA/U,IAGAyS,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAEA,CAAAjG,EAAA6T,UAAArH,CAAA,GAAAA,EAAA,EACA,MAAA+F,UAAA,mCAAA,EAEA,GAAA,CAAAvS,EAAA4T,SAAArH,CAAA,EACA,MAAAgG,UAAA,uBAAA,EAEA,GAAAxE,IAAAzO,IAAA,CAAA8U,EAAAhR,KAAA2K,EAAAA,EAAAnK,SAAA,EAAA2Q,YAAA,CAAA,EACA,MAAAhC,UAAA,4BAAA,EAEA,GAAA8B,IAAA/U,IAAA,CAAAU,EAAA4T,SAAAS,CAAA,EACA,MAAA9B,UAAA,yBAAA,EASApN,KAAA4I,MAFAA,EADA,oBAAAA,EACA,WAEAA,IAAA,aAAAA,EAAAA,EAAAzO,GAMA6F,KAAAoH,KAAAA,EAMApH,KAAAqH,GAAAA,EAMArH,KAAAkP,OAAAA,GAAA/U,GAMA6F,KAAAmK,SAAA,aAAAvB,EAMA5I,KAAA4K,IAAA,CAAA,EAMA5K,KAAAqP,QAAA,KAMArP,KAAAmL,OAAA,KAMAnL,KAAAkK,YAAA,KAMAlK,KAAAsP,aAAA,KAMAtP,KAAAsL,KAAAzQ,CAAAA,CAAAA,EAAAI,MAAAgR,EAAAX,KAAAlE,KAAAjN,GAMA6F,KAAA2L,MAAA,UAAAvE,EAMApH,KAAAiK,aAAA,KAMAjK,KAAAuP,eAAA,KAMAvP,KAAAwP,eAAA,KAMAxP,KAAAiN,QAAAA,CACA,CAlJA8B,EAAAf,SAAA,SAAAvT,EAAAqM,GACA+C,EAAA,IAAAkF,EAAAtU,EAAAqM,EAAAO,GAAAP,EAAAM,KAAAN,EAAA8B,KAAA9B,EAAAoI,OAAApI,EAAAhG,QAAAgG,EAAAmG,OAAA,EAIA,OAHAnG,EAAA0G,UACA3D,EAAA4D,EAAA3G,EAAA0G,SACA3D,EAAAqE,EAAA,SACArE,CACA,EAoJA/K,OAAA2Q,eAAAV,EAAA7O,UAAA,WAAA,CACAsJ,IAAA,WACA,MAAA,oBAAAxJ,KAAA8N,EAAA4B,cACA,CACA,CAAA,EAQA5Q,OAAA2Q,eAAAV,EAAA7O,UAAA,WAAA,CACAsJ,IAAA,WACA,MAAA,CAAAxJ,KAAAuM,QACA,CACA,CAAA,EASAzN,OAAA2Q,eAAAV,EAAA7O,UAAA,YAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAiK,wBAAA+E,GACA,cAAAhP,KAAA8N,EAAA6B,gBACA,CACA,CAAA,EAQA7Q,OAAA2Q,eAAAV,EAAA7O,UAAA,SAAA,CACAsJ,IAAA,WACA,MAAA,WAAAxJ,KAAA8N,EAAA8B,uBACA,CACA,CAAA,EAQA9Q,OAAA2Q,eAAAV,EAAA7O,UAAA,cAAA,CACAsJ,IAAA,WACA,MAAAxJ,CAAAA,KAAAmK,UAAAnK,CAAAA,KAAA4K,MAGA5K,KAAAmL,QACAnL,KAAAwP,gBAAAxP,KAAAuP,gBACA,aAAAvP,KAAA8N,EAAA4B,eACA,CACA,CAAA,EAKAX,EAAA7O,UAAA2P,UAAA,SAAApV,EAAAgF,EAAAqQ,GACA,OAAAlD,EAAA1M,UAAA2P,UAAAlV,KAAAqF,KAAAvF,EAAAgF,EAAAqQ,CAAA,CACA,EAuBAf,EAAA7O,UAAAiO,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAuO,EAAA,EACA,OAAA,aAAAvO,KAAA4I,MAAA5I,KAAA4I,MAAAzO,GACA,OAAA6F,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAkP,OACA,UAAAlP,KAAAc,QACA,UAAAuN,EAAArO,KAAAiN,QAAA9S,GACA,CACA,EAOA4U,EAAA7O,UAAAjE,QAAA,WAEA,IAsCAkG,EAtCA,OAAAnC,KAAA+P,SACA/P,OAEAA,KAAAkK,YAAA+B,EAAAC,SAAAlM,KAAAoH,SAAAjN,IACA6F,KAAAiK,cAAAjK,KAAAwP,gBAAAxP,MAAAgQ,OAAAC,iBAAAjQ,KAAAoH,IAAA,EACApH,KAAAiK,wBAAA+E,EACAhP,KAAAkK,YAAA,KAEAlK,KAAAkK,YAAAlK,KAAAiK,aAAAxB,OAAA3J,OAAAC,KAAAiB,KAAAiK,aAAAxB,MAAA,EAAA,KACAzI,KAAAc,SAAAd,KAAAc,QAAAoP,kBAEAlQ,KAAAkK,YAAA,MAIAlK,KAAAc,SAAA,MAAAd,KAAAc,QAAA,UACAd,KAAAkK,YAAAlK,KAAAc,QAAA,QACAd,KAAAiK,wBAAAP,GAAA,UAAA,OAAA1J,KAAAkK,cACAlK,KAAAkK,YAAAlK,KAAAiK,aAAAxB,OAAAzI,KAAAkK,eAIAlK,KAAAc,UACAd,KAAAc,QAAAsL,SAAAjS,IAAA6F,CAAAA,KAAAiK,cAAAjK,KAAAiK,wBAAAP,GACA,OAAA1J,KAAAc,QAAAsL,OACAtN,OAAAC,KAAAiB,KAAAc,OAAA,EAAAlF,SACAoE,KAAAc,QAAA3G,KAIA6F,KAAAsL,MACAtL,KAAAkK,YAAArP,EAAAI,KAAAkV,WAAAnQ,KAAAkK,YAAA,MAAAlK,KAAAoH,KAAA,IAAApH,GAAA,EAGAlB,OAAAsR,QACAtR,OAAAsR,OAAApQ,KAAAkK,WAAA,GAEAlK,KAAA2L,OAAA,UAAA,OAAA3L,KAAAkK,cAEArP,EAAAwB,OAAA4B,KAAA+B,KAAAkK,WAAA,EACArP,EAAAwB,OAAAwB,OAAAmC,KAAAkK,YAAA/H,EAAAtH,EAAAwV,UAAAxV,EAAAwB,OAAAT,OAAAoE,KAAAkK,WAAA,CAAA,EAAA,CAAA,EAEArP,EAAAyL,KAAAG,MAAAzG,KAAAkK,YAAA/H,EAAAtH,EAAAwV,UAAAxV,EAAAyL,KAAA1K,OAAAoE,KAAAkK,WAAA,CAAA,EAAA,CAAA,EACAlK,KAAAkK,YAAA/H,GAIAnC,KAAA4K,IACA5K,KAAAsP,aAAAzU,EAAAyV,YACAtQ,KAAAmK,SACAnK,KAAAsP,aAAAzU,EAAA0V,WAEAvQ,KAAAsP,aAAAtP,KAAAkK,YAGAlK,KAAAgQ,kBAAAhB,IACAhP,KAAAgQ,OAAAQ,KAAAtQ,UAAAF,KAAAvF,MAAAuF,KAAAsP,cAEA1C,EAAA1M,UAAAjE,QAAAtB,KAAAqF,IAAA,EACA,EAQA+O,EAAA7O,UAAAuQ,EAAA,SAAAjD,GACA,IAaApG,EAbA,MAAA,WAAAoG,GAAA,WAAAA,EACA,IAGAO,EAAA,GAEA,aAAA/N,KAAA4I,OACAmF,EAAA2B,eAAA,mBAEA1P,KAAAgQ,QAAA/D,EAAAC,SAAAlM,KAAAoH,QAAAjN,KAIAiN,EAAApH,KAAAgQ,OAAAxG,IAAAxJ,KAAAoH,KAAA1B,MAAA,GAAA,EAAAgL,IAAA,CAAA,IACAtJ,aAAA4H,GAAA5H,EAAAuJ,QACA5C,EAAA4B,iBAAA,aAGA,CAAA,IAAA3P,KAAA4Q,UAAA,QAAA,EACA7C,EAAA6B,wBAAA,SACA,CAAA,IAAA5P,KAAA4Q,UAAA,QAAA,IACA7C,EAAA6B,wBAAA,YAEA7B,EACA,EAKAgB,EAAA7O,UAAAqN,EAAA,SAAAC,GACA,OAAAZ,EAAA1M,UAAAqN,EAAA5S,KAAAqF,KAAAA,KAAAyN,GAAAD,CAAA,CACA,EAsBAuB,EAAA8B,EAAA,SAAAC,EAAAC,EAAAC,EAAA1B,GAUA,MAPA,YAAA,OAAAyB,EACAA,EAAAlW,EAAAoW,aAAAF,CAAA,EAAAtW,KAGAsW,GAAA,UAAA,OAAAA,IACAA,EAAAlW,EAAAqW,aAAAH,CAAA,EAAAtW,MAEA,SAAAyF,EAAAiR,GACAtW,EAAAoW,aAAA/Q,EAAA6M,WAAA,EACAyB,IAAA,IAAAO,EAAAoC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAA9B,CAAA,CAAA,CAAA,CACA,CACA,EAgBAP,EAAAsC,EAAA,SAAAC,GACAtC,EAAAsC,CACA,C,iDCncA,IAAA/W,EAAAa,EAAAR,QAAAU,EAAA,EAAA,EAEAf,EAAAgX,MAAA,QAoDAhX,EAAAiX,KAjCA,SAAA3Q,EAAA4Q,EAAA1Q,GAMA,OAHA0Q,EAFA,YAAA,OAAAA,GACA1Q,EAAA0Q,EACA,IAAAlX,EAAAmX,MACAD,GACA,IAAAlX,EAAAmX,MACAF,KAAA3Q,EAAAE,CAAA,CACA,EA0CAxG,EAAAoX,SANA,SAAA9Q,EAAA4Q,GAGA,OADAA,EADAA,GACA,IAAAlX,EAAAmX,MACAC,SAAA9Q,CAAA,CACA,EAKAtG,EAAAqX,QAAAtW,EAAA,EAAA,EACAf,EAAAsX,QAAAvW,EAAA,EAAA,EACAf,EAAAuX,SAAAxW,EAAA,EAAA,EACAf,EAAAgQ,UAAAjP,EAAA,EAAA,EAGAf,EAAAqS,iBAAAtR,EAAA,EAAA,EACAf,EAAAsS,UAAAvR,EAAA,EAAA,EACAf,EAAAmX,KAAApW,EAAA,EAAA,EACAf,EAAAmP,KAAApO,EAAA,EAAA,EACAf,EAAAyU,KAAA1T,EAAA,EAAA,EACAf,EAAAwU,MAAAzT,EAAA,EAAA,EACAf,EAAAwX,MAAAzW,EAAA,EAAA,EACAf,EAAAyX,SAAA1W,EAAA,EAAA,EACAf,EAAA0X,QAAA3W,EAAA,EAAA,EACAf,EAAA2X,OAAA5W,EAAA,EAAA,EAGAf,EAAA4X,QAAA7W,EAAA,EAAA,EACAf,EAAA6X,SAAA9W,EAAA,EAAA,EAGAf,EAAA0R,MAAA3Q,EAAA,EAAA,EACAf,EAAAM,KAAAS,EAAA,EAAA,EAGAf,EAAAqS,iBAAAyE,EAAA9W,EAAAmX,IAAA,EACAnX,EAAAsS,UAAAwE,EAAA9W,EAAAyU,KAAAzU,EAAA0X,QAAA1X,EAAAmP,IAAA,EACAnP,EAAAmX,KAAAL,EAAA9W,EAAAyU,IAAA,EACAzU,EAAAwU,MAAAsC,EAAA9W,EAAAyU,IAAA,C,2ICtGA,IAAAzU,EAAAK,EA2BA,SAAAO,IACAZ,EAAAM,KAAAwW,EAAA,EACA9W,EAAA8X,OAAAhB,EAAA9W,EAAA+X,YAAA,EACA/X,EAAAgY,OAAAlB,EAAA9W,EAAAiY,YAAA,CACA,CAvBAjY,EAAAgX,MAAA,UAGAhX,EAAA8X,OAAA/W,EAAA,EAAA,EACAf,EAAA+X,aAAAhX,EAAA,EAAA,EACAf,EAAAgY,OAAAjX,EAAA,EAAA,EACAf,EAAAiY,aAAAlX,EAAA,EAAA,EAGAf,EAAAM,KAAAS,EAAA,EAAA,EACAf,EAAAkY,IAAAnX,EAAA,EAAA,EACAf,EAAAmY,MAAApX,EAAA,EAAA,EACAf,EAAAY,UAAAA,EAcAA,EAAA,C,mEClCAZ,EAAAa,EAAAR,QAAAU,EAAA,EAAA,EAEAf,EAAAgX,MAAA,OAGAhX,EAAAoY,SAAArX,EAAA,EAAA,EACAf,EAAAqY,MAAAtX,EAAA,EAAA,EACAf,EAAAqM,OAAAtL,EAAA,EAAA,EAGAf,EAAAmX,KAAAL,EAAA9W,EAAAyU,KAAAzU,EAAAqY,MAAArY,EAAAqM,MAAA,C,iDCVAxL,EAAAR,QAAAoX,EAGA,IAAAjD,EAAAzT,EAAA,EAAA,EAGA2Q,KAFA+F,EAAA9R,UAAApB,OAAAgO,OAAAiC,EAAA7O,SAAA,GAAA6M,YAAAiF,GAAAhF,UAAA,WAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAcA,SAAA0W,EAAAvX,EAAA4M,EAAAQ,EAAAT,EAAAtG,EAAAmM,GAIA,GAHA8B,EAAApU,KAAAqF,KAAAvF,EAAA4M,EAAAD,EAAAjN,GAAAA,GAAA2G,EAAAmM,CAAA,EAGA,CAAApS,EAAA4T,SAAA5G,CAAA,EACA,MAAAuF,UAAA,0BAAA,EAMApN,KAAA6H,QAAAA,EAMA7H,KAAA6S,gBAAA,KAGA7S,KAAA4K,IAAA,CAAA,CACA,CAuBAoH,EAAAhE,SAAA,SAAAvT,EAAAqM,GACA,OAAA,IAAAkL,EAAAvX,EAAAqM,EAAAO,GAAAP,EAAAe,QAAAf,EAAAM,KAAAN,EAAAhG,QAAAgG,EAAAmG,OAAA,CACA,EAOA+E,EAAA9R,UAAAiO,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAA6H,QACA,OAAA7H,KAAAoH,KACA,KAAApH,KAAAqH,GACA,SAAArH,KAAAkP,OACA,UAAAlP,KAAAc,QACA,UAAAuN,EAAArO,KAAAiN,QAAA9S,GACA,CACA,EAKA6X,EAAA9R,UAAAjE,QAAA,WACA,GAAA+D,KAAA+P,SACA,OAAA/P,KAGA,GAAAiM,EAAAQ,OAAAzM,KAAA6H,WAAA1N,GACA,MAAA6D,MAAA,qBAAAgC,KAAA6H,OAAA,EAEA,OAAAkH,EAAA7O,UAAAjE,QAAAtB,KAAAqF,IAAA,CACA,EAYAgS,EAAAnB,EAAA,SAAAC,EAAAgC,EAAAC,GAUA,MAPA,YAAA,OAAAA,EACAA,EAAAlY,EAAAoW,aAAA8B,CAAA,EAAAtY,KAGAsY,GAAA,UAAA,OAAAA,IACAA,EAAAlY,EAAAqW,aAAA6B,CAAA,EAAAtY,MAEA,SAAAyF,EAAAiR,GACAtW,EAAAoW,aAAA/Q,EAAA6M,WAAA,EACAyB,IAAA,IAAAwD,EAAAb,EAAAL,EAAAgC,EAAAC,CAAA,CAAA,CACA,CACA,C,2CC5HA3X,EAAAR,QAAAuX,EAEA,IAAAtX,EAAAS,EAAA,EAAA,EASA,SAAA6W,EAAAa,GAEA,GAAAA,EACA,IAAA,IAAAjU,EAAAD,OAAAC,KAAAiU,CAAA,EAAAnW,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAmD,KAAAjB,EAAAlC,IAAAmW,EAAAjU,EAAAlC,GACA,CAyBAsV,EAAArF,OAAA,SAAAkG,GACA,OAAAhT,KAAAiT,MAAAnG,OAAAkG,CAAA,CACA,EAUAb,EAAArV,OAAA,SAAAuS,EAAA6D,GACA,OAAAlT,KAAAiT,MAAAnW,OAAAuS,EAAA6D,CAAA,CACA,EAUAf,EAAAgB,gBAAA,SAAA9D,EAAA6D,GACA,OAAAlT,KAAAiT,MAAAE,gBAAA9D,EAAA6D,CAAA,CACA,EAWAf,EAAAtU,OAAA,SAAAuV,GACA,OAAApT,KAAAiT,MAAApV,OAAAuV,CAAA,CACA,EAWAjB,EAAAkB,gBAAA,SAAAD,GACA,OAAApT,KAAAiT,MAAAI,gBAAAD,CAAA,CACA,EASAjB,EAAAmB,OAAA,SAAAjE,GACA,OAAArP,KAAAiT,MAAAK,OAAAjE,CAAA,CACA,EASA8C,EAAA3H,WAAA,SAAA+I,GACA,OAAAvT,KAAAiT,MAAAzI,WAAA+I,CAAA,CACA,EAUApB,EAAAtH,SAAA,SAAAwE,EAAAvO,GACA,OAAAd,KAAAiT,MAAApI,SAAAwE,EAAAvO,CAAA,CACA,EAMAqR,EAAAjS,UAAAiO,OAAA,WACA,OAAAnO,KAAAiT,MAAApI,SAAA7K,KAAAnF,EAAAuT,aAAA,CACA,C,+BCvIAhT,EAAAR,QAAAsX,EAGA,IAAAtF,EAAAtR,EAAA,EAAA,EAGAT,KAFAqX,EAAAhS,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAmF,GAAAlF,UAAA,SAEA1R,EAAA,EAAA,GAiBA,SAAA4W,EAAAzX,EAAA2M,EAAAoM,EAAA5R,EAAA6R,EAAAC,EAAA5S,EAAAmM,EAAA0G,GAYA,GATA9Y,EAAAsU,SAAAsE,CAAA,GACA3S,EAAA2S,EACAA,EAAAC,EAAAvZ,IACAU,EAAAsU,SAAAuE,CAAA,IACA5S,EAAA4S,EACAA,EAAAvZ,IAIAiN,IAAAjN,IAAAU,CAAAA,EAAA4T,SAAArH,CAAA,EACA,MAAAgG,UAAA,uBAAA,EAGA,GAAA,CAAAvS,EAAA4T,SAAA+E,CAAA,EACA,MAAApG,UAAA,8BAAA,EAGA,GAAA,CAAAvS,EAAA4T,SAAA7M,CAAA,EACA,MAAAwL,UAAA,+BAAA,EAEAR,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAAoH,KAAAA,GAAA,MAMApH,KAAAwT,YAAAA,EAMAxT,KAAAyT,cAAAA,CAAAA,CAAAA,GAAAtZ,GAMA6F,KAAA4B,aAAAA,EAMA5B,KAAA0T,eAAAA,CAAAA,CAAAA,GAAAvZ,GAMA6F,KAAA4T,oBAAA,KAMA5T,KAAA6T,qBAAA,KAMA7T,KAAAiN,QAAAA,EAKAjN,KAAA2T,cAAAA,CACA,CAsBAzB,EAAAlE,SAAA,SAAAvT,EAAAqM,GACA,OAAA,IAAAoL,EAAAzX,EAAAqM,EAAAM,KAAAN,EAAA0M,YAAA1M,EAAAlF,aAAAkF,EAAA2M,cAAA3M,EAAA4M,eAAA5M,EAAAhG,QAAAgG,EAAAmG,QAAAnG,EAAA6M,aAAA,CACA,EAOAzB,EAAAhS,UAAAiO,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,OAAA,QAAA7K,KAAAoH,MAAApH,KAAAoH,MAAAjN,GACA,cAAA6F,KAAAwT,YACA,gBAAAxT,KAAAyT,cACA,eAAAzT,KAAA4B,aACA,iBAAA5B,KAAA0T,eACA,UAAA1T,KAAAc,QACA,UAAAuN,EAAArO,KAAAiN,QAAA9S,GACA,gBAAA6F,KAAA2T,cACA,CACA,EAKAzB,EAAAhS,UAAAjE,QAAA,WAGA,OAAA+D,KAAA+P,SACA/P,MAEAA,KAAA4T,oBAAA5T,KAAAgQ,OAAA8D,WAAA9T,KAAAwT,WAAA,EACAxT,KAAA6T,qBAAA7T,KAAAgQ,OAAA8D,WAAA9T,KAAA4B,YAAA,EAEAgL,EAAA1M,UAAAjE,QAAAtB,KAAAqF,IAAA,EACA,C,qCC9JA5E,EAAAR,QAAAiS,EAGA,IAOAmC,EACAiD,EACAvI,EATAkD,EAAAtR,EAAA,EAAA,EAGAyT,KAFAlC,EAAA3M,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAF,GAAAG,UAAA,YAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EACAyW,EAAAzW,EAAA,EAAA,EAoCA,SAAAyY,EAAAC,EAAA5F,GACA,GAAA4F,CAAAA,GAAAA,CAAAA,EAAApY,OACA,OAAAzB,GAEA,IADA,IAAA8Z,EAAA,GACApX,EAAA,EAAAA,EAAAmX,EAAApY,OAAA,EAAAiB,EACAoX,EAAAD,EAAAnX,GAAApC,MAAAuZ,EAAAnX,GAAAsR,OAAAC,CAAA,EACA,OAAA6F,CACA,CA2CA,SAAApH,EAAApS,EAAAqG,GACA8L,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAA+G,OAAA5M,GAOA6F,KAAAkU,EAAA,KASAlU,KAAAmU,EAAA,GAOAnU,KAAAoU,EAAA,CAAA,EAOApU,KAAAqU,EAAA,CAAA,CACA,CAEA,SAAAC,EAAAC,GACAA,EAAAL,EAAA,KACAK,EAAAJ,EAAA,GAIA,IADA,IAAAnE,EAAAuE,EACAvE,EAAAA,EAAAA,QACAA,EAAAmE,EAAA,GAEA,OAAAI,CACA,CA/GA1H,EAAAmB,SAAA,SAAAvT,EAAAqM,GACA,OAAA,IAAA+F,EAAApS,EAAAqM,EAAAhG,OAAA,EAAA0T,QAAA1N,EAAAC,MAAA,CACA,EAkBA8F,EAAAkH,YAAAA,EAQAlH,EAAA8B,aAAA,SAAArB,EAAAjG,GACA,GAAAiG,EACA,IAAA,IAAAzQ,EAAA,EAAAA,EAAAyQ,EAAA1R,OAAA,EAAAiB,EACA,GAAA,UAAA,OAAAyQ,EAAAzQ,IAAAyQ,EAAAzQ,GAAA,IAAAwK,GAAAiG,EAAAzQ,GAAA,GAAAwK,EACA,MAAA,CAAA,EACA,MAAA,CAAA,CACA,EAQAwF,EAAA+B,eAAA,SAAAtB,EAAA7S,GACA,GAAA6S,EACA,IAAA,IAAAzQ,EAAA,EAAAA,EAAAyQ,EAAA1R,OAAA,EAAAiB,EACA,GAAAyQ,EAAAzQ,KAAApC,EACA,MAAA,CAAA,EACA,MAAA,CAAA,CACA,EAuEAqE,OAAA2Q,eAAA5C,EAAA3M,UAAA,cAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAkU,IAAAlU,KAAAkU,EAAArZ,EAAA4Z,QAAAzU,KAAA+G,MAAA,EACA,CACA,CAAA,EA0BA8F,EAAA3M,UAAAiO,OAAA,SAAAC,GACA,OAAAvT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAc,QACA,SAAAiT,EAAA/T,KAAA0U,YAAAtG,CAAA,EACA,CACA,EAOAvB,EAAA3M,UAAAsU,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAA5N,EAAA6N,EAAA9V,OAAAC,KAAA4V,CAAA,EAAA9X,EAAA,EAAAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EACAkK,EAAA4N,EAAAC,EAAA/X,IAJAmD,KAKAwO,KACAzH,EAAAG,SAAA/M,GACA6U,EACAjI,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAA8N,UAAA1a,GACA8X,EACAlL,EAAAM,KAAAlN,GACA4U,EACAlC,GAPAmB,SAOA4G,EAAA/X,GAAAkK,CAAA,CACA,EAGA,OAAA/G,IACA,EAOA6M,EAAA3M,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,IACA,EASAoS,EAAA3M,UAAA4U,QAAA,SAAAra,GACA,GAAAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,aAAAiP,EACA,OAAA1J,KAAA+G,OAAAtM,GAAAgO,OACA,MAAAzK,MAAA,iBAAAvD,CAAA,CACA,EASAoS,EAAA3M,UAAAsO,IAAA,SAAA+E,GAEA,GAAA,EAAAA,aAAAxE,GAAAwE,EAAArE,SAAA/U,IAAAoZ,aAAAvE,GAAAuE,aAAAxB,GAAAwB,aAAA7J,GAAA6J,aAAAtB,GAAAsB,aAAA1G,GACA,MAAAO,UAAA,sCAAA,EAEA,GAAApN,KAAA+G,OAEA,CACA,IAAAgO,EAAA/U,KAAAwJ,IAAA+J,EAAA9Y,IAAA,EACA,GAAAsa,EAAA,CACA,GAAAA,EAAAA,aAAAlI,GAAA0G,aAAA1G,IAAAkI,aAAA/F,GAAA+F,aAAA9C,EAWA,MAAAjU,MAAA,mBAAAuV,EAAA9Y,KAAA,QAAAuF,IAAA,EARA,IADA,IAAA+G,EAAAgO,EAAAL,YACA7X,EAAA,EAAAA,EAAAkK,EAAAnL,OAAA,EAAAiB,EACA0W,EAAA/E,IAAAzH,EAAAlK,EAAA,EACAmD,KAAA8O,OAAAiG,CAAA,EACA/U,KAAA+G,SACA/G,KAAA+G,OAAA,IACAwM,EAAAyB,WAAAD,EAAAjU,QAAA,CAAA,CAAA,CAIA,CACA,MAjBAd,KAAA+G,OAAA,GAkBA/G,KAAA+G,OAAAwM,EAAA9Y,MAAA8Y,EAEAvT,gBAAAgP,GAAAhP,gBAAAiS,GAAAjS,gBAAA0J,GAAA1J,gBAAA+O,GAEAwE,EAAA9F,IAEA8F,EAAA9F,EAAA8F,EAAArF,GAIAlO,KAAAoU,EAAA,CAAA,EACApU,KAAAqU,EAAA,CAAA,EAIA,IADA,IAAArE,EAAAhQ,KACAgQ,EAAAA,EAAAA,QACAA,EAAAoE,EAAA,CAAA,EACApE,EAAAqE,EAAA,CAAA,EAIA,OADAd,EAAA0B,MAAAjV,IAAA,EACAsU,EAAAtU,IAAA,CACA,EASA6M,EAAA3M,UAAA4O,OAAA,SAAAyE,GAEA,GAAA,EAAAA,aAAA3G,GACA,MAAAQ,UAAA,mCAAA,EACA,GAAAmG,EAAAvD,SAAAhQ,KACA,MAAAhC,MAAAuV,EAAA,uBAAAvT,IAAA,EAOA,OALA,OAAAA,KAAA+G,OAAAwM,EAAA9Y,MACAqE,OAAAC,KAAAiB,KAAA+G,MAAA,EAAAnL,SACAoE,KAAA+G,OAAA5M,IAEAoZ,EAAA2B,SAAAlV,IAAA,EACAsU,EAAAtU,IAAA,CACA,EAQA6M,EAAA3M,UAAAnF,OAAA,SAAAyK,EAAAsB,GAEA,GAAAjM,EAAA4T,SAAAjJ,CAAA,EACAA,EAAAA,EAAAE,MAAA,GAAA,OACA,GAAA,CAAAhK,MAAAyZ,QAAA3P,CAAA,EACA,MAAA4H,UAAA,cAAA,EACA,GAAA5H,GAAAA,EAAA5J,QAAA,KAAA4J,EAAA,GACA,MAAAxH,MAAA,uBAAA,EAGA,IADA,IAAAoX,EAAApV,KACA,EAAAwF,EAAA5J,QAAA,CACA,IAAAyZ,EAAA7P,EAAAK,MAAA,EACA,GAAAuP,EAAArO,QAAAqO,EAAArO,OAAAsO,IAEA,GAAA,GADAD,EAAAA,EAAArO,OAAAsO,cACAxI,GACA,MAAA7O,MAAA,2CAAA,CAAA,MAEAoX,EAAA5G,IAAA4G,EAAA,IAAAvI,EAAAwI,CAAA,CAAA,CACA,CAGA,OAFAvO,GACAsO,EAAAZ,QAAA1N,CAAA,EACAsO,CACA,EAMAvI,EAAA3M,UAAAoV,WAAA,WACA,GAAAtV,KAAAqU,EAAA,CAEArU,KAAAuV,EAAAvV,KAAAyN,CAAA,EAEA,IAAA1G,EAAA/G,KAAA0U,YAAA7X,EAAA,EAEA,IADAmD,KAAA/D,QAAA,EACAY,EAAAkK,EAAAnL,QACAmL,EAAAlK,aAAAgQ,EACA9F,EAAAlK,CAAA,IAAAyY,WAAA,EAEAvO,EAAAlK,CAAA,IAAAZ,QAAA,EACA+D,KAAAqU,EAAA,CAAA,CAXA,CAYA,OAAArU,IACA,EAKA6M,EAAA3M,UAAAqV,EAAA,SAAA/H,GAUA,OATAxN,KAAAoU,IACApU,KAAAoU,EAAA,CAAA,EAEA5G,EAAAxN,KAAAyN,GAAAD,EAEAZ,EAAA1M,UAAAqV,EAAA5a,KAAAqF,KAAAwN,CAAA,EACAxN,KAAA0U,YAAAhH,QAAA3G,IACAA,EAAAwO,EAAA/H,CAAA,CACA,CAAA,GACAxN,IACA,EASA6M,EAAA3M,UAAAsV,OAAA,SAAAhQ,EAAAiQ,EAAAC,GAQA,GANA,WAAA,OAAAD,GACAC,EAAAD,EACAA,EAAAtb,IACAsb,GAAA,CAAA/Z,MAAAyZ,QAAAM,CAAA,IACAA,EAAA,CAAAA,IAEA5a,EAAA4T,SAAAjJ,CAAA,GAAAA,EAAA5J,OAAA,CACA,GAAA,MAAA4J,EACA,OAAAxF,KAAAyR,KACAjM,EAAAA,EAAAE,MAAA,GAAA,CACA,MAAA,GAAA,CAAAF,EAAA5J,OACA,OAAAoE,KAEA,IAAA2V,EAAAnQ,EAAA7H,KAAA,GAAA,EAGA,GAAA,KAAA6H,EAAA,GACA,OAAAxF,KAAAyR,KAAA+D,OAAAhQ,EAAA9H,MAAA,CAAA,EAAA+X,CAAA,EAGA,IAAAG,EAAA5V,KAAAyR,KAAAoE,GAAA7V,KAAAyR,KAAAoE,EAAA,IAAAF,GACA,GAAAC,IAAA,CAAAH,GAAAA,CAAAA,EAAA3J,QAAA8J,EAAA7I,WAAA,GACA,OAAA6I,EAKA,IADAA,EAAA5V,KAAA8V,EAAAtQ,EAAAmQ,CAAA,KACA,CAAAF,GAAAA,CAAAA,EAAA3J,QAAA8J,EAAA7I,WAAA,GACA,OAAA6I,EAGA,GAAAF,CAAAA,EAKA,IADA,IAAAK,EAAA/V,KACA+V,EAAA/F,QAAA,CAEA,IADA4F,EAAAG,EAAA/F,OAAA8F,EAAAtQ,EAAAmQ,CAAA,KACA,CAAAF,GAAAA,CAAAA,EAAA3J,QAAA8J,EAAA7I,WAAA,GACA,OAAA6I,EAEAG,EAAAA,EAAA/F,MACA,CACA,OAAA,IACA,EASAnD,EAAA3M,UAAA4V,EAAA,SAAAtQ,EAAAmQ,GACA,GAAA7W,OAAAoB,UAAA8V,eAAArb,KAAAqF,KAAAmU,EAAAwB,CAAA,EACA,OAAA3V,KAAAmU,EAAAwB,GAIA,IAAAC,EAAA5V,KAAAwJ,IAAAhE,EAAA,EAAA,EACAyQ,EAAA,KACA,GAAAL,EACA,IAAApQ,EAAA5J,OACAqa,EAAAL,EACAA,aAAA/I,IACArH,EAAAA,EAAA9H,MAAA,CAAA,EACAuY,EAAAL,EAAAE,EAAAtQ,EAAAA,EAAA7H,KAAA,GAAA,CAAA,QAKA,IAAA,IAAAd,EAAA,EAAAA,EAAAmD,KAAA0U,YAAA9Y,OAAA,EAAAiB,EACAmD,KAAAkU,EAAArX,aAAAgQ,IAAA+I,EAAA5V,KAAAkU,EAAArX,GAAAiZ,EAAAtQ,EAAAmQ,CAAA,KACAM,EAAAL,GAKA,OADA5V,KAAAmU,EAAAwB,GAAAM,CAEA,EAoBApJ,EAAA3M,UAAA4T,WAAA,SAAAtO,GACA,IAAAoQ,EAAA5V,KAAAwV,OAAAhQ,EAAA,CAAAwJ,EAAA,EACA,GAAA4G,EAEA,OAAAA,EADA,MAAA5X,MAAA,iBAAAwH,CAAA,CAEA,EASAqH,EAAA3M,UAAAgW,WAAA,SAAA1Q,GACA,IAAAoQ,EAAA5V,KAAAwV,OAAAhQ,EAAA,CAAAkE,EAAA,EACA,GAAAkM,EAEA,OAAAA,EADA,MAAA5X,MAAA,iBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EASA6M,EAAA3M,UAAA+P,iBAAA,SAAAzK,GACA,IAAAoQ,EAAA5V,KAAAwV,OAAAhQ,EAAA,CAAAwJ,EAAAtF,EAAA,EACA,GAAAkM,EAEA,OAAAA,EADA,MAAA5X,MAAA,yBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EASA6M,EAAA3M,UAAAiW,cAAA,SAAA3Q,GACA,IAAAoQ,EAAA5V,KAAAwV,OAAAhQ,EAAA,CAAAyM,EAAA,EACA,GAAA2D,EAEA,OAAAA,EADA,MAAA5X,MAAA,oBAAAwH,EAAA,QAAAxF,IAAA,CAEA,EAGA6M,EAAAwE,EAAA,SAAAC,EAAA8E,EAAAC,GACArH,EAAAsC,EACAW,EAAAmE,EACA1M,EAAA2M,CACA,C,kDChiBAjb,EAAAR,QAAAgS,GAEAI,UAAA,mBAEA,MAAA+E,EAAAzW,EAAA,EAAA,EACA,IAEAoW,EAFA7W,EAAAS,EAAA,EAAA,EAMAgb,EAAA,CAAAC,UAAA,OAAA7G,eAAA,WAAA8G,YAAA,QAAA7G,iBAAA,kBAAAC,wBAAA,SAAA6G,gBAAA,QAAA,EACAC,EAAA,CAAAH,UAAA,SAAA7G,eAAA,WAAA8G,YAAA,qBAAA7G,iBAAA,kBAAAC,wBAAA,WAAA6G,gBAAA,MAAA,EACAE,EAAA,CAAAJ,UAAA,OAAA7G,eAAA,WAAA8G,YAAA,QAAA7G,iBAAA,kBAAAC,wBAAA,SAAA6G,gBAAA,QAAA,EAUA,SAAA7J,EAAAnS,EAAAqG,GAEA,GAAA,CAAAjG,EAAA4T,SAAAhU,CAAA,EACA,MAAA2S,UAAA,uBAAA,EAEA,GAAAtM,GAAA,CAAAjG,EAAAsU,SAAArO,CAAA,EACA,MAAAsM,UAAA,2BAAA,EAMApN,KAAAc,QAAAA,EAMAd,KAAA2T,cAAA,KAMA3T,KAAAvF,KAAAA,EAOAuF,KAAAyN,EAAA,KAQAzN,KAAAkO,EAAA,SAOAlO,KAAA8N,EAAA,GAOA9N,KAAA4W,EAAA,CAAA,EAMA5W,KAAAgQ,OAAA,KAMAhQ,KAAA+P,SAAA,CAAA,EAMA/P,KAAAiN,QAAA,KAMAjN,KAAAa,SAAA,IACA,CAEA/B,OAAA+X,iBAAAjK,EAAA1M,UAAA,CAQAuR,KAAA,CACAjI,IAAA,WAEA,IADA,IAAA4L,EAAApV,KACA,OAAAoV,EAAApF,QACAoF,EAAAA,EAAApF,OACA,OAAAoF,CACA,CACA,EAQAhL,SAAA,CACAZ,IAAA,WAGA,IAFA,IAAAhE,EAAA,CAAAxF,KAAAvF,MACA2a,EAAApV,KAAAgQ,OACAoF,GACA5P,EAAAsR,QAAA1B,EAAA3a,IAAA,EACA2a,EAAAA,EAAApF,OAEA,OAAAxK,EAAA7H,KAAA,GAAA,CACA,CACA,CACA,CAAA,EAOAiP,EAAA1M,UAAAiO,OAAA,WACA,MAAAnQ,MAAA,CACA,EAOA4O,EAAA1M,UAAA+U,MAAA,SAAAjF,GACAhQ,KAAAgQ,QAAAhQ,KAAAgQ,SAAAA,GACAhQ,KAAAgQ,OAAAlB,OAAA9O,IAAA,EACAA,KAAAgQ,OAAAA,EACAhQ,KAAA+P,SAAA,CAAA,EACA0B,EAAAzB,EAAAyB,KACAA,aAAAC,GACAD,EAAAsF,EAAA/W,IAAA,CACA,EAOA4M,EAAA1M,UAAAgV,SAAA,SAAAlF,GACAyB,EAAAzB,EAAAyB,KACAA,aAAAC,GACAD,EAAAuF,EAAAhX,IAAA,EACAA,KAAAgQ,OAAA,KACAhQ,KAAA+P,SAAA,CAAA,CACA,EAMAnD,EAAA1M,UAAAjE,QAAA,WAKA,OAJA+D,KAAA+P,UAEA/P,KAAAyR,gBAAAC,IACA1R,KAAA+P,SAAA,CAAA,GACA/P,IACA,EAOA4M,EAAA1M,UAAAqV,EAAA,SAAA/H,GACA,OAAAxN,KAAAuN,EAAAvN,KAAAyN,GAAAD,CAAA,CACA,EAOAZ,EAAA1M,UAAAqN,EAAA,SAAAC,GACA,GAAAxN,CAAAA,KAAA4W,EAAA,CAIA,IAAA1K,EAAA,GAGA,GAAA,CAAAsB,EACA,MAAAxP,MAAA,uBAAAgC,KAAAoK,QAAA,EAGA,IAAA6M,EAAAnY,OAAA+O,OAAA7N,KAAAc,QAAAhC,OAAA+O,OAAA,GAAA7N,KAAAc,QAAAiN,QAAA,EAAA,GACA/N,KAAAyQ,EAAAjD,CAAA,CAAA,EAEA,GAAAxN,KAAAyN,EAAA,CAGA,GAAA,WAAAD,EACAtB,EAAApN,OAAA+O,OAAA,GAAA6I,CAAA,OACA,GAAA,WAAAlJ,EACAtB,EAAApN,OAAA+O,OAAA,GAAA8I,CAAA,MACA,CAAA,GAAA,SAAAnJ,EAGA,MAAAxP,MAAA,oBAAAwP,CAAA,EAFAtB,EAAApN,OAAA+O,OAAA,GAAAyI,CAAA,CAGA,CACAtW,KAAA8N,EAAAhP,OAAA+O,OAAA3B,EAAA+K,GAAA,EAAA,CAGA,KAfA,CAoBA,GAAAjX,KAAAmL,kBAAA4G,EAAA,CACAmF,EAAApY,OAAA+O,OAAA,GAAA7N,KAAAmL,OAAA2C,CAAA,EACA9N,KAAA8N,EAAAhP,OAAA+O,OAAAqJ,EAAAD,GAAA,EAAA,CACA,MAAA,GAAAjX,CAAAA,KAAAwP,eAEA,CAAA,GAAAxP,CAAAA,KAAAgQ,OAIA,MAAAhS,MAAA,+BAAAgC,KAAAoK,QAAA,EAHAwD,EAAA9O,OAAA+O,OAAA,GAAA7N,KAAAgQ,OAAAlC,CAAA,EACA9N,KAAA8N,EAAAhP,OAAA+O,OAAAD,EAAAqJ,GAAA,EAAA,CAGA,CACAjX,KAAAuP,iBAEAvP,KAAAuP,eAAAzB,EAAA9N,KAAA8N,EAlBA,CAFA9N,KAAA4W,EAAA,CAAA,CAzBA,CAgDA,EAQAhK,EAAA1M,UAAAuQ,EAAA,WACA,MAAA,EACA,EAOA7D,EAAA1M,UAAA0Q,UAAA,SAAAnW,GACA,OAAAuF,KAAAc,QACAd,KAAAc,QAAArG,GACAN,EACA,EASAyS,EAAA1M,UAAA2P,UAAA,SAAApV,EAAAgF,EAAAqQ,GAUA,OATA9P,KAAAc,UACAd,KAAAc,QAAA,IACA,cAAA7C,KAAAxD,CAAA,EACAI,EAAAsc,YAAAnX,KAAAc,QAAArG,EAAAgF,EAAAqQ,CAAA,EACAA,GAAA9P,KAAAc,QAAArG,KAAAN,KACA6F,KAAA4Q,UAAAnW,CAAA,IAAAgF,IAAAO,KAAA+P,SAAA,CAAA,GACA/P,KAAAc,QAAArG,GAAAgF,GAGAO,IACA,EASA4M,EAAA1M,UAAAkX,gBAAA,SAAA3c,EAAAgF,EAAA4X,GACArX,KAAA2T,gBACA3T,KAAA2T,cAAA,IAEA,IAIA2D,EAgBAC,EApBA5D,EAAA3T,KAAA2T,cAyBA,OAxBA0D,GAGAC,EAAA3D,EAAA6D,KAAA,SAAAF,GACA,OAAAxY,OAAAoB,UAAA8V,eAAArb,KAAA2c,EAAA7c,CAAA,CACA,CAAA,IAIAgd,EAAAH,EAAA7c,GACAI,EAAAsc,YAAAM,EAAAJ,EAAA5X,CAAA,KAGA6X,EAAA,IACA7c,GAAAI,EAAAsc,YAAA,GAAAE,EAAA5X,CAAA,EACAkU,EAAApW,KAAA+Z,CAAA,KAIAC,EAAA,IACA9c,GAAAgF,EACAkU,EAAApW,KAAAga,CAAA,GAGAvX,IACA,EAQA4M,EAAA1M,UAAA8U,WAAA,SAAAlU,EAAAgP,GACA,GAAAhP,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,CAAA,EAAAjE,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAmD,KAAA6P,UAAA9Q,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAAiT,CAAA,EACA,OAAA9P,IACA,EAMA4M,EAAA1M,UAAAzB,SAAA,WACA,IAAAuO,EAAAhN,KAAA+M,YAAAC,UACA5C,EAAApK,KAAAoK,SACA,OAAAA,EAAAxO,OACAoR,EAAA,IAAA5C,EACA4C,CACA,EAMAJ,EAAA1M,UAAAqO,EAAA,WACA,OAAAvO,KAAAyN,GAAA,WAAAzN,KAAAyN,EAKAzN,KAAAyN,EAFAtT,EAGA,EAGAyS,EAAAyE,EAAA,SAAAqG,GACAhG,EAAAgG,CACA,C,qCCxXAtc,EAAAR,QAAAmX,EAGA,IAAAnF,EAAAtR,EAAA,EAAA,EAGAyT,KAFAgD,EAAA7R,UAAApB,OAAAgO,OAAAF,EAAA1M,SAAA,GAAA6M,YAAAgF,GAAA/E,UAAA,QAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EAYA,SAAAyW,EAAAtX,EAAAkd,EAAA7W,EAAAmM,GAQA,GAPAvR,MAAAyZ,QAAAwC,CAAA,IACA7W,EAAA6W,EACAA,EAAAxd,IAEAyS,EAAAjS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAGA6W,IAAAxd,IAAAuB,CAAAA,MAAAyZ,QAAAwC,CAAA,EACA,MAAAvK,UAAA,6BAAA,EAMApN,KAAAiI,MAAA0P,GAAA,GAOA3X,KAAA0K,YAAA,GAMA1K,KAAAiN,QAAAA,CACA,CAyCA,SAAA2K,EAAA3P,GACA,GAAAA,EAAA+H,OACA,IAAA,IAAAnT,EAAA,EAAAA,EAAAoL,EAAAyC,YAAA9O,OAAA,EAAAiB,EACAoL,EAAAyC,YAAA7N,GAAAmT,QACA/H,EAAA+H,OAAAxB,IAAAvG,EAAAyC,YAAA7N,EAAA,CACA,CA9BAkV,EAAA/D,SAAA,SAAAvT,EAAAqM,GACA,OAAA,IAAAiL,EAAAtX,EAAAqM,EAAAmB,MAAAnB,EAAAhG,QAAAgG,EAAAmG,OAAA,CACA,EAOA8E,EAAA7R,UAAAiO,OAAA,SAAAC,GACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAc,QACA,QAAAd,KAAAiI,MACA,UAAAoG,EAAArO,KAAAiN,QAAA9S,GACA,CACA,EAqBA4X,EAAA7R,UAAAsO,IAAA,SAAA3E,GAGA,GAAAA,aAAAkF,EASA,OANAlF,EAAAmG,QAAAnG,EAAAmG,SAAAhQ,KAAAgQ,QACAnG,EAAAmG,OAAAlB,OAAAjF,CAAA,EACA7J,KAAAiI,MAAA1K,KAAAsM,EAAApP,IAAA,EACAuF,KAAA0K,YAAAnN,KAAAsM,CAAA,EAEA+N,EADA/N,EAAAsB,OAAAnL,IACA,EACAA,KARA,MAAAoN,UAAA,uBAAA,CASA,EAOA2E,EAAA7R,UAAA4O,OAAA,SAAAjF,GAGA,GAAA,EAAAA,aAAAkF,GACA,MAAA3B,UAAA,uBAAA,EAEA,IAAAtR,EAAAkE,KAAA0K,YAAAoB,QAAAjC,CAAA,EAGA,GAAA/N,EAAA,EACA,MAAAkC,MAAA6L,EAAA,uBAAA7J,IAAA,EAUA,OARAA,KAAA0K,YAAAnK,OAAAzE,EAAA,CAAA,EAIA,CAAA,GAHAA,EAAAkE,KAAAiI,MAAA6D,QAAAjC,EAAApP,IAAA,IAIAuF,KAAAiI,MAAA1H,OAAAzE,EAAA,CAAA,EAEA+N,EAAAsB,OAAA,KACAnL,IACA,EAKA+R,EAAA7R,UAAA+U,MAAA,SAAAjF,GACApD,EAAA1M,UAAA+U,MAAAta,KAAAqF,KAAAgQ,CAAA,EAGA,IAFA,IAEAnT,EAAA,EAAAA,EAAAmD,KAAAiI,MAAArM,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAAmG,EAAAxG,IAAAxJ,KAAAiI,MAAApL,EAAA,EACAgN,GAAA,CAAAA,EAAAsB,SACAtB,EAAAsB,OALAnL,MAMA0K,YAAAnN,KAAAsM,CAAA,CAEA,CAEA+N,EAAA5X,IAAA,CACA,EAKA+R,EAAA7R,UAAAgV,SAAA,SAAAlF,GACA,IAAA,IAAAnG,EAAAhN,EAAA,EAAAA,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,GACAgN,EAAA7J,KAAA0K,YAAA7N,IAAAmT,QACAnG,EAAAmG,OAAAlB,OAAAjF,CAAA,EACA+C,EAAA1M,UAAAgV,SAAAva,KAAAqF,KAAAgQ,CAAA,CACA,EAUAlR,OAAA2Q,eAAAsC,EAAA7R,UAAA,mBAAA,CACAsJ,IAAA,WACA,IAIAK,EAJA,OAAA,MAAA7J,KAAA0K,aAAA,IAAA1K,KAAA0K,YAAA9O,SAKA,OADAiO,EAAA7J,KAAA0K,YAAA,IACA5J,SAAA,CAAA,IAAA+I,EAAA/I,QAAA,gBACA,CACA,CAAA,EAkBAiR,EAAAlB,EAAA,WAGA,IAFA,IAAA8G,EAAAjc,MAAAC,UAAAC,MAAA,EACAE,EAAA,EACAA,EAAAH,UAAAC,QACA+b,EAAA7b,GAAAH,UAAAG,CAAA,IACA,OAAA,SAAAoE,EAAA2X,GACAhd,EAAAoW,aAAA/Q,EAAA6M,WAAA,EACAyB,IAAA,IAAAuD,EAAA8F,EAAAF,CAAA,CAAA,EACA7Y,OAAA2Q,eAAAvP,EAAA2X,EAAA,CACArO,IAAA3O,EAAAid,YAAAH,CAAA,EACAI,IAAAld,EAAAmd,YAAAL,CAAA,CACA,CAAA,CACA,CACA,C,4CC5NAvc,EAAAR,QAAAgY,IAEA/R,SAAA,KACA+R,GAAA1G,SAAA,CAAA+L,SAAA,CAAA,CAAA,EAEA,IAAAtF,EAAArX,EAAA,EAAA,EACAoW,EAAApW,EAAA,EAAA,EACA0T,EAAA1T,EAAA,EAAA,EACAyT,EAAAzT,EAAA,EAAA,EACA0W,EAAA1W,EAAA,EAAA,EACAyW,EAAAzW,EAAA,EAAA,EACAoO,EAAApO,EAAA,EAAA,EACA2W,EAAA3W,EAAA,EAAA,EACA4W,EAAA5W,EAAA,EAAA,EACAsR,EAAAtR,EAAA,EAAA,EACA2Q,EAAA3Q,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAEA4c,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,GAAA,2BACAC,GAAA,+DAkCA,SAAA9F,GAAApU,EAAAiT,EAAA3Q,GAEA2Q,aAAAC,IACA5Q,EAAA2Q,EACAA,EAAA,IAAAC,GAKA,IASAiH,EACAC,EACAC,EA0yBAC,EAvnBAA,EACAC,EA/LAC,GAFAlY,EADAA,GACA8R,GAAA1G,UAEA8M,uBAAA,CAAA,EACAC,EAAAtG,EAAAnU,EAAAsC,EAAAoY,sBAAA,CAAA,CAAA,EACAC,EAAAF,EAAAE,KACA5b,EAAA0b,EAAA1b,KACA6b,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,EAAA,CAAA,EAIA/L,EAAA,SAEA4H,EAAA3D,EAEA+H,EAAA,GACAC,EAAA,GAEAC,EAAA5Y,EAAAmX,SAAA,SAAAxd,GAAA,OAAAA,CAAA,EAAAI,EAAA8e,UAaA,SAAAC,EAAAd,EAAAre,EAAAof,GACA,IAAAhZ,EAAA+R,GAAA/R,SAGA,OAFAgZ,IACAjH,GAAA/R,SAAA,MACA7C,MAAA,YAAAvD,GAAA,SAAA,KAAAqe,EAAA,OAAAjY,EAAAA,EAAA,KAAA,IAAA,QAAAoY,EAAAa,KAAA,GAAA,CACA,CAEA,SAAAC,IACA,IACAjB,EADArQ,EAAA,GAEA,GAEA,GAAA,OAAAqQ,EAAAK,EAAA,IAAA,MAAAL,EACA,MAAAc,EAAAd,CAAA,CAAA,OAEArQ,EAAAlL,KAAA4b,EAAA,CAAA,EACAE,EAAAP,CAAA,EAEA,OADAA,EAAAM,EAAA,IACA,MAAAN,GACA,OAAArQ,EAAA9K,KAAA,EAAA,CACA,CAEA,SAAAqc,EAAAC,GACA,IAAAnB,EAAAK,EAAA,EACA,OAAAL,GACA,IAAA,IACA,IAAA,IAEA,OADAvb,EAAAub,CAAA,EACAiB,EAAA,EACA,IAAA,OAAA,IAAA,OACA,MAAA,CAAA,EACA,IAAA,QAAA,IAAA,QACA,MAAA,CAAA,CACA,CACA,IACAG,IAoDApB,EApDAA,EAoDAe,EApDA,CAAA,EAqDAxX,EAAA,EAKA,OAJA,MAAAyW,EAAA,IAAAA,MACAzW,EAAA,CAAA,EACAyW,EAAAA,EAAAqB,UAAA,CAAA,GAEArB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAzW,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,CACA,CACA,GAAAmV,EAAAja,KAAA6a,CAAA,EACA,OAAAzW,EAAA+X,SAAAtB,EAAA,EAAA,EACA,GAAAV,EAAAna,KAAA6a,CAAA,EACA,OAAAzW,EAAA+X,SAAAtB,EAAA,EAAA,EACA,GAAAR,EAAAra,KAAA6a,CAAA,EACA,OAAAzW,EAAA+X,SAAAtB,EAAA,CAAA,EAGA,GAAAN,EAAAva,KAAA6a,CAAA,EACA,OAAAzW,EAAAgY,WAAAvB,CAAA,EAGA,MAAAc,EAAAd,EAAA,SAAAe,CAAA,CAtEA,CAPA,MAAAvU,GAEA,GAAA2U,GAAAvB,GAAAza,KAAA6a,CAAA,EACA,OAAAA,EAGA,MAAAc,EAAAd,EAAA,OAAA,CACA,CACA,CAEA,SAAAwB,EAAAC,EAAAC,GACA,IAAAxd,EACA,GACA,GAAAwd,CAAAA,GAAA,OAAA1B,EAAAM,EAAA,IAAA,MAAAN,EAOA,IACAyB,EAAAhd,KAAA,CAAAP,EAAAyd,EAAAtB,EAAA,CAAA,EAAAE,EAAA,KAAA,CAAA,CAAA,EAAAoB,EAAAtB,EAAA,CAAA,EAAAnc,EAAA,CAOA,CANA,MAAAb,GACA,GAAAqe,EAAAA,GAAA9B,GAAAza,KAAA6a,CAAA,GAAA,MAAAtL,GAGA,MAAArR,EAFAoe,EAAAhd,KAAAub,CAAA,CAIA,KAfA,CACA,IAAA4B,EAAAX,EAAA,EAEA,GADAQ,EAAAhd,KAAAmd,CAAA,EACA,MAAAlN,EACA,MAAAoM,EAAAc,EAAA,IAAA,CAEA,CAUA,OACArB,EAAA,IAAA,CAAA,CAAA,GACA,IAAAsB,EAAA,CAAA7Z,QAAA3G,GACA0V,UAAA,SAAApV,EAAAgF,GACAO,KAAAc,UAAA3G,KAAA6F,KAAAc,QAAA,IACAd,KAAAc,QAAArG,GAAAgF,CACA,CAJA,EAKAmb,EACAD,EACA,SAAA7B,GAEA,GAAA,WAAAA,EAIA,MAAAc,EAAAd,CAAA,EAHA+B,EAAAF,EAAA7B,CAAA,EACAO,EAAA,GAAA,CAGA,EACA,WACAyB,EAAAH,CAAA,CACA,CAAA,CACA,CA+BA,SAAAF,EAAA3B,EAAAiC,GACA,OAAAjC,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,CACA,CAGA,GAAAiC,GAAA,MAAAjC,EAAA,IAAAA,IAAA,CAGA,GAAAX,EAAAla,KAAA6a,CAAA,EACA,OAAAsB,SAAAtB,EAAA,EAAA,EACA,GAAAT,EAAApa,KAAA6a,CAAA,EACA,OAAAsB,SAAAtB,EAAA,EAAA,EAGA,GAAAP,EAAAta,KAAA6a,CAAA,EACA,OAAAsB,SAAAtB,EAAA,CAAA,CATA,CAYA,MAAAc,EAAAd,EAAA,IAAA,CACA,CA8DA,SAAAkC,EAAAhL,EAAA8I,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA+B,EAAA7K,EAAA8I,CAAA,EACAO,EAAA,GAAA,EACA,EAEA,IAAA,UAEA,OADA4B,EAAAjL,CAAA,EACA,EAEA,IAAA,OAEA,OADAkL,EAAAlL,CAAA,EACA,EAEA,IAAA,UACAmL,IAodAC,EANApL,EA9cAA,EA8cA8I,EA9cAA,EAidA,GAAAL,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EAhdA,OAodAyB,EADAQ,EAAA,IAAAnJ,EAAA6G,CAAA,EACA,SAAAA,GACA,GAAAkC,CAAAA,EAAAI,EAAAtC,CAAA,EAAA,CAKA,GAAA,QAAAA,EAGA,MAAAc,EAAAd,CAAA,EAFAuC,IAUArL,EAVAoL,EAaAE,EAAAhC,EAAA,EAEAlS,EAAA0R,EAGA,GAAA,CAAAL,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,EAAA,MAAA,EAEA,IACAtF,EAAAC,EACAC,EAFAjZ,EAAAqe,EASA,GALAO,EAAA,GAAA,EACAA,EAAA,SAAA,CAAA,CAAA,IACA5F,EAAA,CAAA,GAGA,CAAAiF,GAAAza,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,CAAA,EAQA,GANAtF,EAAAsF,EACAO,EAAA,GAAA,EAAAA,EAAA,SAAA,EAAAA,EAAA,GAAA,EACAA,EAAA,SAAA,CAAA,CAAA,IACA3F,EAAA,CAAA,GAGA,CAAAgF,GAAAza,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,CAAA,EAEAlX,EAAAkX,EACAO,EAAA,GAAA,EAEA,IAAAkC,EAAA,IAAArJ,EAAAzX,EAAA2M,EAAAoM,EAAA5R,EAAA6R,EAAAC,CAAA,EACA6H,EAAAtO,QAAAqO,EACAV,EAAAW,EAAA,SAAAzC,GAGA,GAAA,WAAAA,EAIA,MAAAc,EAAAd,CAAA,EAHA+B,EAAAU,EAAAzC,CAAA,EACAO,EAAA,GAAA,CAIA,CAAA,EACArJ,EAAAxB,IAAA+M,CAAA,CA7DA,CAOA,CAAA,EACAvL,EAAAxB,IAAA4M,CAAA,EACApL,IAAAoF,GACAoE,EAAAjc,KAAA6d,CAAA,EAjeA,EAidA,MAAAxB,EAAAd,EAAA,cAAA,EA/cA,IAAA,SACA0C,IA0hBAC,EANAzL,EAphBAA,EAohBA8I,EAphBAA,EAuhBA,GAAAJ,GAAAza,KAAA6a,EAAAK,EAAA,CAAA,EAthBA,OAyhBAsC,EAAA3C,EACA8B,EAAA,KAAA,SAAA9B,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACA4C,EAAA1L,EAAA8I,EAAA2C,CAAA,EACA,MAEA,IAAA,WAGAC,EAAA1L,EADA,WAAAxC,EACA,kBAEA,WAFAiO,CAAA,EAIA,MAEA,QAEA,GAAA,WAAAjO,GAAA,CAAAkL,GAAAza,KAAA6a,CAAA,EACA,MAAAc,EAAAd,CAAA,EACAvb,EAAAub,CAAA,EACA4C,EAAA1L,EAAA,WAAAyL,CAAA,CAEA,CACA,CAAA,EAnjBA,EAuhBA,MAAA7B,EAAAd,EAAA,WAAA,CAthBA,CAEA,CAEA,SAAA8B,EAAA3G,EAAA0H,EAAAC,GACA,IAQA9C,EARA+C,EAAA5C,EAAAa,KAOA,GANA7F,IACA,UAAA,OAAAA,EAAAhH,UACAgH,EAAAhH,QAAAqM,EAAA,GAEArF,EAAApT,SAAA+R,GAAA/R,UAEAwY,EAAA,IAAA,CAAA,CAAA,EAAA,CAEA,KAAA,OAAAP,EAAAK,EAAA,IACAwC,EAAA7C,CAAA,EACAO,EAAA,IAAA,CAAA,CAAA,CACA,MACAuC,GACAA,EAAA,EACAvC,EAAA,GAAA,EACApF,IAAA,UAAA,OAAAA,EAAAhH,SAAA+L,KACA/E,EAAAhH,QAAAqM,EAAAuC,CAAA,GAAA5H,EAAAhH,QAEA,CAEA,SAAAgO,EAAAjL,EAAA8I,GAGA,GAAA,CAAAL,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,EAAA,WAAA,EAEA,IAAA1R,EAAA,IAAA4H,EAAA8J,CAAA,EACA8B,EAAAxT,EAAA,SAAA0R,GACA,GAAAkC,CAAAA,EAAA5T,EAAA0R,CAAA,EAGA,OAAAA,GAEA,IAAA,MACAgD,IA4KA9L,EA5KA5I,EA8KAS,GADAwR,EAAA,GAAA,EACAF,EAAA,GAGA,GAAAlN,EAAAQ,OAAA5E,KAAA1N,GACA,MAAAyf,EAAA/R,EAAA,MAAA,EAEAwR,EAAA,GAAA,EACA,IAAA0C,EAAA5C,EAAA,EAGA,GAAA,CAAAT,GAAAza,KAAA8d,CAAA,EACA,MAAAnC,EAAAmC,EAAA,MAAA,EAEA1C,EAAA,GAAA,EACA,IAAA5e,EAAA0e,EAAA,EAGA,GAAA,CAAAV,GAAAxa,KAAAxD,CAAA,EACA,MAAAmf,EAAAnf,EAAA,MAAA,EAEA4e,EAAA,GAAA,EACA,IAAAxP,EAAA,IAAAmI,EAAA0H,EAAAjf,CAAA,EAAAggB,EAAAtB,EAAA,CAAA,EAAAtR,EAAAkU,CAAA,EACAnB,EAAA/Q,EAAA,SAAAiP,GAGA,GAAA,WAAAA,EAIA,MAAAc,EAAAd,CAAA,EAHA+B,EAAAhR,EAAAiP,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAyB,EAAAjR,CAAA,CACA,CAAA,EACAmG,EAAAxB,IAAA3E,CAAA,EA/MA,MAEA,IAAA,WACA,GAAA,WAAA2D,EACA,MAAAoM,EAAAd,CAAA,EAEA,IAAA,WACA4C,EAAAtU,EAAA0R,CAAA,EACA,MAEA,IAAA,WAEA,GAAA,WAAAtL,EACAkO,EAAAtU,EAAA,iBAAA,MACA,CAAA,GAAA,WAAAoG,EACA,MAAAoM,EAAAd,CAAA,EAEA4C,EAAAtU,EAAA,UAAA,CACA,CACA,MAEA,IAAA,QA6LA4I,EA5LA5I,EA4LA0R,EA5LAA,EA+LA,GAAA,CAAAL,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,EAAA,MAAA,EAEA,IAAA7Q,EAAA,IAAA8J,EAAA2H,EAAAZ,CAAA,CAAA,EACA8B,EAAA3S,EAAA,SAAA6Q,GACA,WAAAA,GACA+B,EAAA5S,EAAA6Q,CAAA,EACAO,EAAA,GAAA,IAEA9b,EAAAub,CAAA,EACA4C,EAAAzT,EAAA,UAAA,EAEA,CAAA,EACA+H,EAAAxB,IAAAvG,CAAA,EA3MA,MAEA,IAAA,aACAqS,EAAAlT,EAAA4U,aAAA5U,EAAA4U,WAAA,GAAA,EACA,MAEA,IAAA,WACA1B,EAAAlT,EAAAkG,WAAAlG,EAAAkG,SAAA,IAAA,CAAA,CAAA,EACA,MAEA,QAEA,GAAA,WAAAE,GAAA,CAAAkL,GAAAza,KAAA6a,CAAA,EACA,MAAAc,EAAAd,CAAA,EAGAvb,EAAAub,CAAA,EACA4C,EAAAtU,EAAA,UAAA,CAEA,CACA,CAAA,EACA4I,EAAAxB,IAAApH,CAAA,EACA4I,IAAAoF,GACAoE,EAAAjc,KAAA6J,CAAA,CAEA,CAEA,SAAAsU,EAAA1L,EAAApH,EAAAsG,GACA,IAAA9H,EAAA+R,EAAA,EACA,GAAA,UAAA/R,EAAA,CACA6U,IAyDAjM,EAzDAA,EAyDApH,EAzDAA,EA0DA,GAAA,MAAA4E,EACA,MAAAoM,EAAA,OAAA,EAEA,IAWAxS,EAEAyC,EAbApP,EAAA0e,EAAA,EAGA,GAAAV,GAAAxa,KAAAxD,CAAA,EA/DA,OAkEA0W,EAAAtW,EAAAqhB,QAAAzhB,CAAA,EACAA,IAAA0W,IACA1W,EAAAI,EAAAshB,QAAA1hB,CAAA,GACA4e,EAAA,GAAA,EACAhS,EAAAoT,EAAAtB,EAAA,CAAA,GACA/R,EAAA,IAAA4H,EAAAvU,CAAA,GACAkW,MAAA,CAAA,GAEA9G,EADA,IAAAkF,EAAAoC,EAAA9J,EAAA5M,EAAAmO,CAAA,GACA/H,SAAA+R,GAAA/R,SACA+Z,EAAAxT,EAAA,SAAA0R,GACA,OAAAA,GAEA,IAAA,SACA+B,EAAAzT,EAAA0R,CAAA,EACAO,EAAA,GAAA,EACA,MACA,IAAA,WACA,IAAA,WACAqC,EAAAtU,EAAA0R,CAAA,EACA,MAEA,IAAA,WAGA4C,EAAAtU,EADA,WAAAoG,EACA,kBAEA,UAFA,EAIA,MAEA,IAAA,UACAyN,EAAA7T,CAAA,EACA,MAEA,IAAA,OACA8T,EAAA9T,CAAA,EACA,MAEA,IAAA,WACAkT,EAAAlT,EAAAkG,WAAAlG,EAAAkG,SAAA,IAAA,CAAA,CAAA,EACA,MAGA,QACA,MAAAsM,EAAAd,CAAA,CACA,CACA,CAAA,EAtCAjP,KAuCAmG,EAAAxB,IAAApH,CAAA,EACAoH,IAAA3E,CAAA,EAlDA,MAAA+P,EAAAnf,EAAA,MAAA,CA/DA,CAQA,KAAA2M,EAAAgV,SAAA,GAAA,GAAAhD,EAAA,EAAAiD,WAAA,GAAA,GACAjV,GAAA+R,EAAA,EAIA,GAAA,CAAAT,GAAAza,KAAAmJ,CAAA,EACA,MAAAwS,EAAAxS,EAAA,MAAA,EAEA,IAAA3M,EAAA0e,EAAA,EAIA,GAAA,CAAAV,GAAAxa,KAAAxD,CAAA,EACA,MAAAmf,EAAAnf,EAAA,MAAA,EAEAA,EAAAif,EAAAjf,CAAA,EACA4e,EAAA,GAAA,EAEA,IAAAxP,EAAA,IAAAkF,EAAAtU,EAAAggB,EAAAtB,EAAA,CAAA,EAAA/R,EAAAwB,EAAAsG,CAAA,EAEA0L,EAAA/Q,EAAA,SAAAiP,GAGA,GAAA,WAAAA,EAIA,MAAAc,EAAAd,CAAA,EAHA+B,EAAAhR,EAAAiP,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAyB,EAAAjR,CAAA,CACA,CAAA,EAEA,oBAAAjB,GAEAX,EAAA,IAAA8J,EAAA,IAAAtX,CAAA,EACAoP,EAAAgG,UAAA,kBAAA,CAAA,CAAA,EACA5H,EAAAuG,IAAA3E,CAAA,EACAmG,EAAAxB,IAAAvG,CAAA,GAEA+H,EAAAxB,IAAA3E,CAAA,EAEAmG,IAAAoF,GACAoE,EAAAjc,KAAAsM,CAAA,CAEA,CAyHA,SAAAqR,EAAAlL,EAAA8I,GAGA,GAAA,CAAAL,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,EAAA,MAAA,EAEA,IAAA7K,EAAA,IAAAvE,EAAAoP,CAAA,EACA8B,EAAA3M,EAAA,SAAA6K,GACA,OAAAA,GACA,IAAA,SACA+B,EAAA5M,EAAA6K,CAAA,EACAO,EAAA,GAAA,EACA,MAEA,IAAA,WACAiB,EAAArM,EAAAX,WAAAW,EAAAX,SAAA,IAAA,CAAA,CAAA,EACAW,EAAAX,WAAAnT,KAAA8T,EAAAX,SAAA,IACA,MAEA,QACAgP,IASAtM,EATA/B,EASA6K,EATAA,EAYA,GAAA,CAAAL,GAAAxa,KAAA6a,CAAA,EACA,MAAAc,EAAAd,EAAA,MAAA,EAEAO,EAAA,GAAA,EACA,IAAA5Z,EAAAgb,EAAAtB,EAAA,EAAA,CAAA,CAAA,EACAwB,EAAA,CACA7Z,QAAA3G,GAEAyW,UAAA,SAAAnW,GACA,OAAAuF,KAAAc,QAAArG,EACA,EACAoV,UAAA,SAAApV,EAAAgF,GACAmN,EAAA1M,UAAA2P,UAAAlV,KAAAggB,EAAAlgB,EAAAgF,CAAA,CACA,EACA2X,gBAAA,WACA,OAAAjd,EACA,CATA,EAnBAmiB,OA6BA1B,EAAAD,EAAA,SAAA7B,GAGA,GAAA,WAAAA,EAIA,MAAAc,EAAAd,CAAA,EAHA+B,EAAAF,EAAA7B,CAAA,EACAO,EAAA,GAAA,CAIA,EAAA,WACAyB,EAAAH,CAAA,CACA,CAAA,EAXAC,KAYA5K,EAAAxB,IAAAsK,EAAArZ,EAAAkb,EAAA1N,QAAA0N,EAAAhH,eAAAgH,EAAA7Z,OAAA,CAxCA,CACA,CAAA,EACAkP,EAAAxB,IAAAP,CAAA,EACA+B,IAAAoF,GACAoE,EAAAjc,KAAA0Q,CAAA,CAEA,CAqCA,SAAA4M,EAAA7K,EAAA8I,GACA,IAEAyD,EAAA,CAAA,EAKA,IAJA,WAAAzD,IACAA,EAAAK,EAAA,GAGA,MAAAL,GAAA,CAMA,GALA,MAAAA,IACA0D,EAAArD,EAAA,EACAE,EAAA,GAAA,EACAP,EAAA,IAAA0D,EAAA,KAEAD,EAAA,CAEA,GADAA,EAAA,CAAA,EACAzD,EAAA2D,SAAA,GAAA,GAAA,CAAA3D,EAAA2D,SAAA,GAAA,EAAA,CACA,IAAAC,EAAA5D,EAAApT,MAAA,GAAA,EACAiX,EAAAD,EAAA,GAAA,IACA5D,EAAA4D,EAAA,GACA,QACA,CACAC,EAAA7D,CACA,MACAzB,EAAAA,EAAAA,EAAAyB,EAAAA,EAEAA,EAAAK,EAAA,CACA,CACA,IA+EA1e,EAAA4c,EA/EA5c,EAAA4c,EAAAsF,EAAAC,OAAAvF,CAAA,EAAAsF,EACAE,EAMA,SAAAC,EAAA9M,EAAAvV,GAEA,GAAA4e,EAAA,IAAA,CAAA,CAAA,EAAA,CAGA,IAFA,IAAA0D,EAAA,GAEA,CAAA1D,EAAA,IAAA,CAAA,CAAA,GAAA,CAEA,GAAA,CAAAZ,GAAAxa,KAAA6a,EAAAK,EAAA,CAAA,EACA,MAAAS,EAAAd,EAAA,MAAA,EAEA,GAAA,OAAAA,EACA,MAAAc,EAAAd,EAAA,cAAA,EAGA,IAAArZ,EAYAud,EAXA3F,EAAAyB,EAIA,GAFAO,EAAA,IAAA,CAAA,CAAA,EAEA,MAAAD,EAAA,EAIA3Z,EAAAqd,EAAA9M,EAAAvV,EAAA,IAAAqe,CAAA,OACA,GAAA,MAAAM,EAAA,GAGA,GAFA3Z,EAAA,GAEA4Z,EAAA,IAAA,CAAA,CAAA,EAAA,CACA,KACA2D,EAAAhD,EAAA,CAAA,CAAA,EACAva,EAAAlC,KAAAyf,CAAA,EACA3D,EAAA,IAAA,CAAA,CAAA,IACAA,EAAA,GAAA,EACA,KAAA,IAAA2D,GACAnN,EAAAG,EAAAvV,EAAA,IAAAqe,EAAAkE,CAAA,CAEA,CAAA,MAEAvd,EAAAua,EAAA,CAAA,CAAA,EACAnK,EAAAG,EAAAvV,EAAA,IAAAqe,EAAArZ,CAAA,EAGA,IAAAwd,EAAAF,EAAA1F,GAEA4F,IACAxd,EAAA,GAAAmd,OAAAK,CAAA,EAAAL,OAAAnd,CAAA,GAEAsd,EAAA1F,GAAA5X,EAGA4Z,EAAA,IAAA,CAAA,CAAA,EACAA,EAAA,IAAA,CAAA,CAAA,CACA,CAEA,OAAA0D,CACA,CAEA,IAAAG,EAAAlD,EAAA,CAAA,CAAA,EACAnK,EAAAG,EAAAvV,EAAAyiB,CAAA,EACA,OAAAA,CAEA,EAnEAlN,EAAAvV,CAAA,EACA4c,EAAAA,GAAA,MAAAA,EAAA,GAAAA,EAAA3Z,MAAA,CAAA,EAAA2Z,EACAsF,EAAAA,GAAA,MAAAA,EAAAA,EAAA/gB,OAAA,GAAA+gB,EAAAjf,MAAA,EAAA,CAAA,CAAA,EAAAif,EA4EAliB,EA3EAkiB,EA2EAld,EA3EAod,EA2EAxF,EA3EAA,GA2EArH,EA3EAA,GA4EAoH,iBACApH,EAAAoH,gBAAA3c,EAAAgF,EAAA4X,CAAA,CA5EA,CAiEA,SAAAxH,EAAAG,EAAAvV,EAAAgF,GACA2V,IAAApF,GAAA,cAAA/R,KAAAxD,CAAA,EACAgf,EAAAhf,GAAAgF,EAGAuQ,EAAAH,WACAG,EAAAH,UAAApV,EAAAgF,CAAA,CACA,CAOA,SAAAqb,EAAA9K,GACA,GAAAqJ,EAAA,IAAA,CAAA,CAAA,EAAA,CACA,KACAwB,EAAA7K,EAAA,QAAA,EACAqJ,EAAA,IAAA,CAAA,CAAA,IACAA,EAAA,GAAA,CACA,CAEA,CAgHA,KAAA,QAAAP,EAAAK,EAAA,IACA,OAAAL,GAEA,IAAA,UAGA,GAAA,CAAAS,EACA,MAAAK,EAAAd,CAAA,EA9oBA,GAAAH,IAAAxe,GACA,MAAAyf,EAAA,SAAA,EAKA,GAHAjB,EAAAQ,EAAA,EAGA,CAAAT,GAAAza,KAAA0a,CAAA,EACA,MAAAiB,EAAAjB,EAAA,MAAA,EAEAvD,EAAAA,EAAAra,OAAA4d,CAAA,EAEAU,EAAA,GAAA,EAsoBA,MAEA,IAAA,SAGA,GAAA,CAAAE,EACA,MAAAK,EAAAd,CAAA,EAtoBA,OADAC,EADAD,EAAAA,KAAAA,EAAAM,EAAA,GAGA,IAAA,OACAL,EAAAF,EAAAA,GAAA,GACAM,EAAA,EACA,MACA,IAAA,SACAA,EAAA,EAEA,QACAJ,EAAAH,EAAAA,GAAA,EAEA,CACAE,EAAAiB,EAAA,EACAV,EAAA,GAAA,EACAN,EAAAxb,KAAAub,CAAA,EA2nBA,MAEA,IAAA,SAGA,GAAA,CAAAS,EACA,MAAAK,EAAAd,CAAA,EAznBA,GAJAO,EAAA,GAAA,GACA7L,EAAAuM,EAAA,GAGA,KACA,MAAAH,EAAApM,EAAA,QAAA,EAEA6L,EAAA,GAAA,EAynBA,MAEA,IAAA,UAEA,GAAA,CAAAE,EACA,MAAAK,EAAAd,CAAA,EArnBA,GALAO,EAAA,GAAA,EACA7L,EAAAuM,EAAA,EAIA,CAHA,CAAA,QAGA0C,SAAAjP,CAAA,EACA,MAAAoM,EAAApM,EAAA,SAAA,EAEA6L,EAAA,GAAA,EAonBA,MAEA,IAAA,SACAwB,EAAAzF,EAAA0D,CAAA,EACAO,EAAA,IAAA,CAAA,CAAA,EACA,MAEA,QAGA,GAAA2B,EAAA5F,EAAA0D,CAAA,EAAA,CACAS,EAAA,CAAA,EACA,QACA,CAGA,MAAAK,EAAAd,CAAA,CACA,CAMA,OA11BAU,EAAA9L,QAAAuG,IACAA,EAAAxG,EAAAD,EACA1O,OAAAC,KAAA0a,CAAA,EAAA/L,QAAA4J,IACArD,EAAArD,UAAA0G,CAAA,IAAAnd,IACA8Z,EAAApE,UAAAyH,EAAAmC,EAAAnC,GAAA,CAAA,CAAA,CACA,CAAA,CACA,CAAA,EAm1BA1E,GAAA/R,SAAA,KACA,CACAsc,QAAAxE,EACAC,QAAAA,EACAC,YAAAA,EACApH,KAAAA,CACA,CACA,C,iGC37BArW,EAAAR,QAAA2X,EAEA,IAEAC,EAFA3X,EAAAS,EAAA,EAAA,EAIA8hB,EAAAviB,EAAAuiB,SACA9W,EAAAzL,EAAAyL,KAGA,SAAA+W,EAAAjK,EAAAkK,GACA,OAAAC,WAAA,uBAAAnK,EAAAhR,IAAA,OAAAkb,GAAA,GAAA,MAAAlK,EAAA7M,GAAA,CACA,CAQA,SAAAgM,EAAAxV,GAMAiD,KAAAmC,IAAApF,EAMAiD,KAAAoC,IAAA,EAMApC,KAAAuG,IAAAxJ,EAAAnB,MACA,CAeA,SAAAkR,IACA,OAAAjS,EAAA2iB,OACA,SAAAzgB,GACA,OAAAwV,EAAAzF,OAAA,SAAA/P,GACA,OAAAlC,EAAA2iB,OAAAC,SAAA1gB,CAAA,EACA,IAAAyV,EAAAzV,CAAA,EAEA2gB,EAAA3gB,CAAA,CACA,GAAAA,CAAA,CACA,EAEA2gB,CACA,CAzBA,IA4CAje,EA5CAie,EAAA,aAAA,OAAAhc,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAhG,MAAAyZ,QAAApY,CAAA,EACA,OAAA,IAAAwV,EAAAxV,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAEA,SAAAjB,GACA,GAAArB,MAAAyZ,QAAApY,CAAA,EACA,OAAA,IAAAwV,EAAAxV,CAAA,EACA,MAAAiB,MAAA,gBAAA,CACA,EAqEA,SAAA2f,IAEA,IAAAC,EAAA,IAAAR,EAAA,EAAA,CAAA,EACAvgB,EAAA,EACA,GAAAmD,EAAA,EAAAA,KAAAuG,IAAAvG,KAAAoC,KAaA,CACA,KAAAvF,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAmD,KAAAoC,KAAApC,KAAAuG,IACA,MAAA8W,EAAArd,IAAA,EAGA,GADA4d,EAAA/Z,IAAA+Z,EAAA/Z,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAwb,CACA,CAGA,OADAA,EAAA/Z,IAAA+Z,EAAA/Z,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,GAAA,MAAA,EAAAvF,KAAA,EACA+gB,CACA,CAzBA,KAAA/gB,EAAA,EAAA,EAAAA,EAGA,GADA+gB,EAAA/Z,IAAA+Z,EAAA/Z,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAwb,EAKA,GAFAA,EAAA/Z,IAAA+Z,EAAA/Z,IAAA,IAAA7D,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EACAwb,EAAA9Z,IAAA8Z,EAAA9Z,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,KAAA,EACApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAwb,EAgBA,GAfA/gB,EAAA,EAeA,EAAAmD,KAAAuG,IAAAvG,KAAAoC,KACA,KAAAvF,EAAA,EAAA,EAAAA,EAGA,GADA+gB,EAAA9Z,IAAA8Z,EAAA9Z,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,EAAA,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAwb,CACA,MAEA,KAAA/gB,EAAA,EAAA,EAAAA,EAAA,CAEA,GAAAmD,KAAAoC,KAAApC,KAAAuG,IACA,MAAA8W,EAAArd,IAAA,EAGA,GADA4d,EAAA9Z,IAAA8Z,EAAA9Z,IAAA,IAAA9D,KAAAmC,IAAAnC,KAAAoC,OAAA,EAAAvF,EAAA,KAAA,EACAmD,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,IACA,OAAAwb,CACA,CAGA,MAAA5f,MAAA,yBAAA,CACA,CAiCA,SAAA6f,EAAA1b,EAAAlF,GACA,OAAAkF,EAAAlF,EAAA,GACAkF,EAAAlF,EAAA,IAAA,EACAkF,EAAAlF,EAAA,IAAA,GACAkF,EAAAlF,EAAA,IAAA,MAAA,CACA,CA8BA,SAAA6gB,IAGA,GAAA9d,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAA8W,EAAArd,KAAA,CAAA,EAEA,OAAA,IAAAod,EAAAS,EAAA7d,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,EAAAyb,EAAA7d,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CAAA,CACA,CA5KAmQ,EAAAzF,OAAAA,EAAA,EAEAyF,EAAArS,UAAA6d,EAAAljB,EAAAa,MAAAwE,UAAA8d,UAAAnjB,EAAAa,MAAAwE,UAAAxC,MAOA6U,EAAArS,UAAA+d,QACAxe,EAAA,WACA,WACA,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,QAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,KAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,IAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,MACA3C,GAAAA,GAAA,GAAAO,KAAAmC,IAAAnC,KAAAoC,OAAA,MAAA,EAAApC,KAAAmC,IAAAnC,KAAAoC,GAAA,IAAA,KAGA,GAAApC,KAAAoC,KAAA,GAAApC,KAAAuG,SAIA,OAAA9G,EAFA,MADAO,KAAAoC,IAAApC,KAAAuG,IACA8W,EAAArd,KAAA,EAAA,CAGA,GAOAuS,EAAArS,UAAAge,MAAA,WACA,OAAA,EAAAle,KAAAie,OAAA,CACA,EAMA1L,EAAArS,UAAAie,OAAA,WACA,IAAA1e,EAAAO,KAAAie,OAAA,EACA,OAAAxe,IAAA,EAAA,EAAA,EAAAA,GAAA,CACA,EAoFA8S,EAAArS,UAAAke,KAAA,WACA,OAAA,IAAApe,KAAAie,OAAA,CACA,EAaA1L,EAAArS,UAAAme,QAAA,WAGA,GAAAre,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAA8W,EAAArd,KAAA,CAAA,EAEA,OAAA6d,EAAA7d,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CACA,EAMAmQ,EAAArS,UAAAoe,SAAA,WAGA,GAAAte,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAA8W,EAAArd,KAAA,CAAA,EAEA,OAAA,EAAA6d,EAAA7d,KAAAmC,IAAAnC,KAAAoC,KAAA,CAAA,CACA,EAkCAmQ,EAAArS,UAAAqe,MAAA,WAGA,GAAAve,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAA8W,EAAArd,KAAA,CAAA,EAEA,IAAAP,EAAA5E,EAAA0jB,MAAAja,YAAAtE,KAAAmC,IAAAnC,KAAAoC,GAAA,EAEA,OADApC,KAAAoC,KAAA,EACA3C,CACA,EAOA8S,EAAArS,UAAAse,OAAA,WAGA,GAAAxe,KAAAoC,IAAA,EAAApC,KAAAuG,IACA,MAAA8W,EAAArd,KAAA,CAAA,EAEA,IAAAP,EAAA5E,EAAA0jB,MAAAvZ,aAAAhF,KAAAmC,IAAAnC,KAAAoC,GAAA,EAEA,OADApC,KAAAoC,KAAA,EACA3C,CACA,EAMA8S,EAAArS,UAAAyL,MAAA,WACA,IAAA/P,EAAAoE,KAAAie,OAAA,EACAjhB,EAAAgD,KAAAoC,IACAnF,EAAA+C,KAAAoC,IAAAxG,EAGA,GAAAqB,EAAA+C,KAAAuG,IACA,MAAA8W,EAAArd,KAAApE,CAAA,EAGA,OADAoE,KAAAoC,KAAAxG,EACAF,MAAAyZ,QAAAnV,KAAAmC,GAAA,EACAnC,KAAAmC,IAAAzE,MAAAV,EAAAC,CAAA,EAEAD,IAAAC,GACAwhB,EAAA5jB,EAAA2iB,QAEAiB,EAAAxY,MAAA,CAAA,EACA,IAAAjG,KAAAmC,IAAA4K,YAAA,CAAA,EAEA/M,KAAA+d,EAAApjB,KAAAqF,KAAAmC,IAAAnF,EAAAC,CAAA,CACA,EAMAsV,EAAArS,UAAA5D,OAAA,WACA,IAAAqP,EAAA3L,KAAA2L,MAAA,EACA,OAAArF,EAAAE,KAAAmF,EAAA,EAAAA,EAAA/P,MAAA,CACA,EAOA2W,EAAArS,UAAAmZ,KAAA,SAAAzd,GACA,GAAA,UAAA,OAAAA,EAAA,CAEA,GAAAoE,KAAAoC,IAAAxG,EAAAoE,KAAAuG,IACA,MAAA8W,EAAArd,KAAApE,CAAA,EACAoE,KAAAoC,KAAAxG,CACA,MACA,GAEA,GAAAoE,KAAAoC,KAAApC,KAAAuG,IACA,MAAA8W,EAAArd,IAAA,CAAA,OACA,IAAAA,KAAAmC,IAAAnC,KAAAoC,GAAA,KAEA,OAAApC,IACA,EAOAuS,EAAArS,UAAAwe,SAAA,SAAAlS,GACA,OAAAA,GACA,KAAA,EACAxM,KAAAqZ,KAAA,EACA,MACA,KAAA,EACArZ,KAAAqZ,KAAA,CAAA,EACA,MACA,KAAA,EACArZ,KAAAqZ,KAAArZ,KAAAie,OAAA,CAAA,EACA,MACA,KAAA,EACA,KAAA,IAAAzR,EAAA,EAAAxM,KAAAie,OAAA,IACAje,KAAA0e,SAAAlS,CAAA,EAEA,MACA,KAAA,EACAxM,KAAAqZ,KAAA,CAAA,EACA,MAGA,QACA,MAAArb,MAAA,qBAAAwO,EAAA,cAAAxM,KAAAoC,GAAA,CACA,CACA,OAAApC,IACA,EAEAuS,EAAAlB,EAAA,SAAAsN,GACAnM,EAAAmM,EACApM,EAAAzF,OAAAA,EAAA,EACA0F,EAAAnB,EAAA,EAEA,IAAA9V,EAAAV,EAAAI,KAAA,SAAA,WACAJ,EAAA+jB,MAAArM,EAAArS,UAAA,CAEA2e,MAAA,WACA,OAAAlB,EAAAhjB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEAujB,OAAA,WACA,OAAAnB,EAAAhjB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEAwjB,OAAA,WACA,OAAApB,EAAAhjB,KAAAqF,IAAA,EAAAgf,SAAA,EAAAzjB,GAAA,CAAA,CAAA,CACA,EAEA0jB,QAAA,WACA,OAAAnB,EAAAnjB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,EAEA2jB,SAAA,WACA,OAAApB,EAAAnjB,KAAAqF,IAAA,EAAAzE,GAAA,CAAA,CAAA,CACA,CAEA,CAAA,CACA,C,+BC9ZAH,EAAAR,QAAA4X,EAGA,IAAAD,EAAAjX,EAAA,EAAA,EAGAT,IAFA2X,EAAAtS,UAAApB,OAAAgO,OAAAyF,EAAArS,SAAA,GAAA6M,YAAAyF,EAEAlX,EAAA,EAAA,GASA,SAAAkX,EAAAzV,GACAwV,EAAA5X,KAAAqF,KAAAjD,CAAA,CAOA,CAEAyV,EAAAnB,EAAA,WAEAxW,EAAA2iB,SACAhL,EAAAtS,UAAA6d,EAAAljB,EAAA2iB,OAAAtd,UAAAxC,MACA,EAMA8U,EAAAtS,UAAA5D,OAAA,WACA,IAAAiK,EAAAvG,KAAAie,OAAA,EACA,OAAAje,KAAAmC,IAAAgd,UACAnf,KAAAmC,IAAAgd,UAAAnf,KAAAoC,IAAApC,KAAAoC,IAAA3F,KAAA2iB,IAAApf,KAAAoC,IAAAmE,EAAAvG,KAAAuG,GAAA,CAAA,EACAvG,KAAAmC,IAAA1D,SAAA,QAAAuB,KAAAoC,IAAApC,KAAAoC,IAAA3F,KAAA2iB,IAAApf,KAAAoC,IAAAmE,EAAAvG,KAAAuG,GAAA,CAAA,CACA,EASAiM,EAAAnB,EAAA,C,qCCjDAjW,EAAAR,QAAA8W,EAGA,IAQA1C,EACA4D,EACAhM,EAVAiG,EAAAvR,EAAA,EAAA,EAGAyT,KAFA2C,EAAAxR,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAA2E,GAAA1E,UAAA,OAEA1R,EAAA,EAAA,GACAoO,EAAApO,EAAA,EAAA,EACAyW,EAAAzW,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAaA,SAAAoW,EAAA5Q,GACA+L,EAAAlS,KAAAqF,KAAA,GAAAc,CAAA,EAMAd,KAAAqf,SAAA,GAMArf,KAAAsf,MAAA,GAOAtf,KAAAyN,EAAA,SAOAzN,KAAA6V,EAAA,EACA,CAsCA,SAAA0J,KA9BA7N,EAAA1D,SAAA,SAAAlH,EAAA2K,GAKA,OAHAA,EADAA,GACA,IAAAC,EACA5K,EAAAhG,SACA2Q,EAAAuD,WAAAlO,EAAAhG,OAAA,EACA2Q,EAAA+C,QAAA1N,EAAAC,MAAA,EAAAuO,WAAA,CACA,EAUA5D,EAAAxR,UAAAsf,YAAA3kB,EAAA2K,KAAAvJ,QAUAyV,EAAAxR,UAAAQ,MAAA7F,EAAA6F,MAaAgR,EAAAxR,UAAAsR,KAAA,SAAAA,EAAA3Q,EAAAC,EAAAC,GACA,YAAA,OAAAD,IACAC,EAAAD,EACAA,EAAA3G,IAEA,IAAAslB,EAAAzf,KACA,GAAA,CAAAe,EACA,OAAAlG,EAAA8F,UAAA6Q,EAAAiO,EAAA5e,EAAAC,CAAA,EAGA,IAAA4e,EAAA3e,IAAAwe,EAGA,SAAAI,EAAAxjB,EAAAsV,GAEA,GAAA1Q,EAAA,CAGA,GAAA2e,EACA,MAAAvjB,EAEAsV,GACAA,EAAA6D,WAAA,EAEA,IAAAsK,EAAA7e,EACAA,EAAA,KACA6e,EAAAzjB,EAAAsV,CAAA,CATA,CAUA,CAGA,SAAAoO,EAAAhf,GACA,IAAAif,EAAAjf,EAAAkf,YAAA,kBAAA,EACA,GAAA,CAAA,EAAAD,EAAA,CACAE,EAAAnf,EAAAsZ,UAAA2F,CAAA,EACA,GAAAE,KAAApZ,EAAA,OAAAoZ,CACA,CACA,OAAA,IACA,CAGA,SAAAC,EAAApf,EAAArC,GACA,IAGA,GAFA3D,EAAA4T,SAAAjQ,CAAA,GAAA,MAAAA,EAAA,IAAAA,MACAA,EAAAoB,KAAAgT,MAAApU,CAAA,GACA3D,EAAA4T,SAAAjQ,CAAA,EAEA,CACAoU,EAAA/R,SAAAA,EACA,IACAkP,EADAmQ,EAAAtN,EAAApU,EAAAihB,EAAA3e,CAAA,EAEAjE,EAAA,EACA,GAAAqjB,EAAAtH,QACA,KAAA/b,EAAAqjB,EAAAtH,QAAAhd,OAAA,EAAAiB,GACAkT,EAAA8P,EAAAK,EAAAtH,QAAA/b,EAAA,GAAA4iB,EAAAD,YAAA3e,EAAAqf,EAAAtH,QAAA/b,EAAA,IACA6D,EAAAqP,CAAA,EACA,GAAAmQ,EAAArH,YACA,IAAAhc,EAAA,EAAAA,EAAAqjB,EAAArH,YAAAjd,OAAA,EAAAiB,GACAkT,EAAA8P,EAAAK,EAAArH,YAAAhc,EAAA,GAAA4iB,EAAAD,YAAA3e,EAAAqf,EAAArH,YAAAhc,EAAA,IACA6D,EAAAqP,EAAA,CAAA,CAAA,CACA,MAdA0P,EAAAzK,WAAAxW,EAAAsC,OAAA,EAAA0T,QAAAhW,EAAAuI,MAAA,CAiBA,CAFA,MAAA5K,GACAwjB,EAAAxjB,CAAA,CACA,CACAujB,GAAAS,GACAR,EAAA,KAAAF,CAAA,CAEA,CAGA,SAAA/e,EAAAG,EAAAuf,GAIA,GAHAvf,EAAAgf,EAAAhf,CAAA,GAAAA,EAGA4e,CAAAA,CAAAA,EAAAH,MAAAxT,QAAAjL,CAAA,EAMA,GAHA4e,EAAAH,MAAA/hB,KAAAsD,CAAA,EAGAA,KAAA+F,EACA8Y,EACAO,EAAApf,EAAA+F,EAAA/F,EAAA,GAEA,EAAAsf,EACAE,WAAA,WACA,EAAAF,EACAF,EAAApf,EAAA+F,EAAA/F,EAAA,CACA,CAAA,QAMA,GAAA6e,EAAA,CACA,IAAAlhB,EACA,IACAA,EAAA3D,EAAA+F,GAAA0f,aAAAzf,CAAA,EAAApC,SAAA,MAAA,CAKA,CAJA,MAAAtC,GAGA,OAFA,KAAAikB,GACAT,EAAAxjB,CAAA,EAEA,CACA8jB,EAAApf,EAAArC,CAAA,CACA,KACA,EAAA2hB,EACAV,EAAA/e,MAAAG,EAAA,SAAA1E,EAAAqC,GACA,EAAA2hB,EAEApf,IAGA5E,EAEAikB,EAEAD,GACAR,EAAA,KAAAF,CAAA,EAFAE,EAAAxjB,CAAA,EAKA8jB,EAAApf,EAAArC,CAAA,EACA,CAAA,CAEA,CACA,IAAA2hB,EAAA,EAIAtlB,EAAA4T,SAAA5N,CAAA,IACAA,EAAA,CAAAA,IAEA,IAAA,IAAAkP,EAAAlT,EAAA,EAAAA,EAAAgE,EAAAjF,OAAA,EAAAiB,GACAkT,EAAA0P,EAAAD,YAAA,GAAA3e,EAAAhE,EAAA,IACA6D,EAAAqP,CAAA,EASA,OARA2P,EACAD,EAAAnK,WAAA,EAGA6K,GACAR,EAAA,KAAAF,CAAA,EAGAA,CACA,EA+BA/N,EAAAxR,UAAAyR,SAAA,SAAA9Q,EAAAC,GACA,GAAAjG,EAAA0lB,OAEA,OAAAvgB,KAAAwR,KAAA3Q,EAAAC,EAAAye,CAAA,EADA,MAAAvhB,MAAA,eAAA,CAEA,EAKA0T,EAAAxR,UAAAoV,WAAA,WACA,GAAA,CAAAtV,KAAAqU,EAAA,OAAArU,KAEA,GAAAA,KAAAqf,SAAAzjB,OACA,MAAAoC,MAAA,4BAAAgC,KAAAqf,SAAAzU,IAAA,SAAAf,GACA,MAAA,WAAAA,EAAAqF,OAAA,QAAArF,EAAAmG,OAAA5F,QACA,CAAA,EAAAzM,KAAA,IAAA,CAAA,EACA,OAAAkP,EAAA3M,UAAAoV,WAAA3a,KAAAqF,IAAA,CACA,EAGA,IAAAwgB,EAAA,SAUA,SAAAC,EAAAhP,EAAA5H,GACA,IAEA6W,EAFAC,EAAA9W,EAAAmG,OAAAwF,OAAA3L,EAAAqF,MAAA,EACA,GAAAyR,EASA,OARAD,EAAA,IAAA3R,EAAAlF,EAAAO,SAAAP,EAAAxC,GAAAwC,EAAAzC,KAAAyC,EAAAjB,KAAAzO,GAAA0P,EAAA/I,OAAA,EAEA6f,EAAAnX,IAAAkX,EAAAjmB,IAAA,KAGAimB,EAAAlR,eAAA3F,GACA0F,eAAAmR,EACAC,EAAAnS,IAAAkS,CAAA,GACA,CAGA,CAQAhP,EAAAxR,UAAA6W,EAAA,SAAAxD,GACA,GAAAA,aAAAxE,EAEAwE,EAAArE,SAAA/U,IAAAoZ,EAAAhE,gBACAkR,EAAAzgB,EAAAuT,CAAA,GACAvT,KAAAqf,SAAA9hB,KAAAgW,CAAA,OAEA,GAAAA,aAAA7J,EAEA8W,EAAAviB,KAAAsV,EAAA9Y,IAAA,IACA8Y,EAAAvD,OAAAuD,EAAA9Y,MAAA8Y,EAAA9K,aAEA,GAAA,EAAA8K,aAAAxB,GAAA,CAEA,GAAAwB,aAAAvE,EACA,IAAA,IAAAnS,EAAA,EAAAA,EAAAmD,KAAAqf,SAAAzjB,QACA6kB,EAAAzgB,EAAAA,KAAAqf,SAAAxiB,EAAA,EACAmD,KAAAqf,SAAA9e,OAAA1D,EAAA,CAAA,EAEA,EAAAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAkW,EAAAmB,YAAA9Y,OAAA,EAAAyB,EACA2C,KAAA+W,EAAAxD,EAAAW,EAAA7W,EAAA,EACAmjB,EAAAviB,KAAAsV,EAAA9Y,IAAA,IACA8Y,EAAAvD,OAAAuD,EAAA9Y,MAAA8Y,EACA,EAEAA,aAAAvE,GAAAuE,aAAA7J,GAAA6J,aAAAxE,KAEA/O,KAAA6V,EAAAtC,EAAAnJ,UAAAmJ,EAMA,EAQA7B,EAAAxR,UAAA8W,EAAA,SAAAzD,GAGA,IAKAzX,EAPA,GAAAyX,aAAAxE,EAEAwE,EAAArE,SAAA/U,KACAoZ,EAAAhE,gBACAgE,EAAAhE,eAAAS,OAAAlB,OAAAyE,EAAAhE,cAAA,EACAgE,EAAAhE,eAAA,MAIA,CAAA,GAFAzT,EAAAkE,KAAAqf,SAAAvT,QAAAyH,CAAA,IAGAvT,KAAAqf,SAAA9e,OAAAzE,EAAA,CAAA,QAIA,GAAAyX,aAAA7J,EAEA8W,EAAAviB,KAAAsV,EAAA9Y,IAAA,GACA,OAAA8Y,EAAAvD,OAAAuD,EAAA9Y,WAEA,GAAA8Y,aAAA1G,EAAA,CAEA,IAAA,IAAAhQ,EAAA,EAAAA,EAAA0W,EAAAmB,YAAA9Y,OAAA,EAAAiB,EACAmD,KAAAgX,EAAAzD,EAAAW,EAAArX,EAAA,EAEA2jB,EAAAviB,KAAAsV,EAAA9Y,IAAA,GACA,OAAA8Y,EAAAvD,OAAAuD,EAAA9Y,KAEA,CAEA,OAAAuF,KAAA6V,EAAAtC,EAAAnJ,SACA,EAGAsH,EAAAL,EAAA,SAAAC,EAAAsP,EAAAC,GACA7R,EAAAsC,EACAsB,EAAAgO,EACAha,EAAAia,CACA,C,uDClZAzlB,EAAAR,QAAA,E,0BCKAA,EA6BAqX,QAAA3W,EAAA,EAAA,C,+BClCAF,EAAAR,QAAAqX,EAEA,IAAApX,EAAAS,EAAA,EAAA,EAsCA,SAAA2W,EAAA6O,EAAAC,EAAAC,GAEA,GAAA,YAAA,OAAAF,EACA,MAAA1T,UAAA,4BAAA,EAEAvS,EAAAkF,aAAApF,KAAAqF,IAAA,EAMAA,KAAA8gB,QAAAA,EAMA9gB,KAAA+gB,iBAAAzS,CAAAA,CAAAyS,EAMA/gB,KAAAghB,kBAAA1S,CAAAA,CAAA0S,CACA,GA3DA/O,EAAA/R,UAAApB,OAAAgO,OAAAjS,EAAAkF,aAAAG,SAAA,GAAA6M,YAAAkF,GAwEA/R,UAAA+gB,QAAA,SAAAA,EAAA1F,EAAA2F,EAAAC,EAAAC,EAAArgB,GAEA,GAAA,CAAAqgB,EACA,MAAAhU,UAAA,2BAAA,EAEA,IAAAqS,EAAAzf,KACA,GAAA,CAAAe,EACA,OAAAlG,EAAA8F,UAAAsgB,EAAAxB,EAAAlE,EAAA2F,EAAAC,EAAAC,CAAA,EAEA,GAAA,CAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAAtf,EAAA/C,MAAA,eAAA,CAAA,CAAA,EAAA,CAAA,EACA7D,GAGA,IACA,OAAAslB,EAAAqB,QACAvF,EACA2F,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,CAAA,EAAAzB,OAAA,EACA,SAAAxjB,EAAAqF,GAEA,GAAArF,EAEA,OADAsjB,EAAAjf,KAAA,QAAArE,EAAAof,CAAA,EACAxa,EAAA5E,CAAA,EAGA,GAAA,OAAAqF,EAEA,OADAie,EAAAxiB,IAAA,CAAA,CAAA,EACA9C,GAGA,GAAA,EAAAqH,aAAA2f,GACA,IACA3f,EAAA2f,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAAxf,CAAA,CAIA,CAHA,MAAArF,GAEA,OADAsjB,EAAAjf,KAAA,QAAArE,EAAAof,CAAA,EACAxa,EAAA5E,CAAA,CACA,CAIA,OADAsjB,EAAAjf,KAAA,OAAAgB,EAAA+Z,CAAA,EACAxa,EAAA,KAAAS,CAAA,CACA,CACA,CAKA,CAJA,MAAArF,GAGA,OAFAsjB,EAAAjf,KAAA,QAAArE,EAAAof,CAAA,EACA8E,WAAA,WAAAtf,EAAA5E,CAAA,CAAA,EAAA,CAAA,EACAhC,EACA,CACA,EAOA8X,EAAA/R,UAAAjD,IAAA,SAAAokB,GAOA,OANArhB,KAAA8gB,UACAO,GACArhB,KAAA8gB,QAAA,KAAA,KAAA,IAAA,EACA9gB,KAAA8gB,QAAA,KACA9gB,KAAAQ,KAAA,KAAA,EAAAH,IAAA,GAEAL,IACA,C,+BC5IA5E,EAAAR,QAAAqX,EAGA,IAAApF,EAAAvR,EAAA,EAAA,EAGA4W,KAFAD,EAAA/R,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAAkF,GAAAjF,UAAA,UAEA1R,EAAA,EAAA,GACAT,EAAAS,EAAA,EAAA,EACAmX,EAAAnX,EAAA,EAAA,EAWA,SAAA2W,EAAAxX,EAAAqG,GACA+L,EAAAlS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAA6U,QAAA,GAOA7U,KAAAshB,EAAA,IACA,CA4DA,SAAAhN,EAAA8G,GAEA,OADAA,EAAAkG,EAAA,KACAlG,CACA,CA/CAnJ,EAAAjE,SAAA,SAAAvT,EAAAqM,GACA,IAAAsU,EAAA,IAAAnJ,EAAAxX,EAAAqM,EAAAhG,OAAA,EAEA,GAAAgG,EAAA+N,QACA,IAAA,IAAAD,EAAA9V,OAAAC,KAAA+H,EAAA+N,OAAA,EAAAhY,EAAA,EAAAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EACAue,EAAA5M,IAAA0D,EAAAlE,SAAA4G,EAAA/X,GAAAiK,EAAA+N,QAAAD,EAAA/X,GAAA,CAAA,EAOA,OANAiK,EAAAC,QACAqU,EAAA5G,QAAA1N,EAAAC,MAAA,EACAD,EAAA0G,UACA4N,EAAA3N,EAAA3G,EAAA0G,SACA4N,EAAAnO,QAAAnG,EAAAmG,QACAmO,EAAAlN,EAAA,SACAkN,CACA,EAOAnJ,EAAA/R,UAAAiO,OAAA,SAAAC,GACA,IAAAmT,EAAA1U,EAAA3M,UAAAiO,OAAAxT,KAAAqF,KAAAoO,CAAA,EACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAuO,EAAA,EACA,UAAAgT,GAAAA,EAAAzgB,SAAA3G,GACA,UAAA0S,EAAAkH,YAAA/T,KAAAwhB,aAAApT,CAAA,GAAA,GACA,SAAAmT,GAAAA,EAAAxa,QAAA5M,GACA,UAAAkU,EAAArO,KAAAiN,QAAA9S,GACA,CACA,EAQA2E,OAAA2Q,eAAAwC,EAAA/R,UAAA,eAAA,CACAsJ,IAAA,WACA,OAAAxJ,KAAAshB,IAAAthB,KAAAshB,EAAAzmB,EAAA4Z,QAAAzU,KAAA6U,OAAA,EACA,CACA,CAAA,EAUA5C,EAAA/R,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAA6U,QAAApa,IACAoS,EAAA3M,UAAAsJ,IAAA7O,KAAAqF,KAAAvF,CAAA,CACA,EAKAwX,EAAA/R,UAAAoV,WAAA,WACA,GAAAtV,KAAAqU,EAAA,CAEAxH,EAAA3M,UAAAjE,QAAAtB,KAAAqF,IAAA,EAEA,IADA,IAAA6U,EAAA7U,KAAAwhB,aACA3kB,EAAA,EAAAA,EAAAgY,EAAAjZ,OAAA,EAAAiB,EACAgY,EAAAhY,GAAAZ,QAAA,CALA,CAMA,OAAA+D,IACA,EAKAiS,EAAA/R,UAAAqV,EAAA,SAAA/H,GASA,OARAxN,KAAAoU,IAEA5G,EAAAxN,KAAAyN,GAAAD,EAEAX,EAAA3M,UAAAqV,EAAA5a,KAAAqF,KAAAwN,CAAA,EACAxN,KAAAwhB,aAAA9T,QAAA6N,IACAA,EAAAhG,EAAA/H,CAAA,CACA,CAAA,GACAxN,IACA,EAKAiS,EAAA/R,UAAAsO,IAAA,SAAA+E,GAGA,GAAAvT,KAAAwJ,IAAA+J,EAAA9Y,IAAA,EACA,MAAAuD,MAAA,mBAAAuV,EAAA9Y,KAAA,QAAAuF,IAAA,EAEA,OAAAuT,aAAArB,EAGAoC,GAFAtU,KAAA6U,QAAAtB,EAAA9Y,MAAA8Y,GACAvD,OAAAhQ,IACA,EAEA6M,EAAA3M,UAAAsO,IAAA7T,KAAAqF,KAAAuT,CAAA,CACA,EAKAtB,EAAA/R,UAAA4O,OAAA,SAAAyE,GACA,GAAAA,aAAArB,EAAA,CAGA,GAAAlS,KAAA6U,QAAAtB,EAAA9Y,QAAA8Y,EACA,MAAAvV,MAAAuV,EAAA,uBAAAvT,IAAA,EAIA,OAFA,OAAAA,KAAA6U,QAAAtB,EAAA9Y,MACA8Y,EAAAvD,OAAA,KACAsE,EAAAtU,IAAA,CACA,CACA,OAAA6M,EAAA3M,UAAA4O,OAAAnU,KAAAqF,KAAAuT,CAAA,CACA,EASAtB,EAAA/R,UAAA4M,OAAA,SAAAgU,EAAAC,EAAAC,GAEA,IADA,IACAzF,EADAkG,EAAA,IAAAhP,EAAAR,QAAA6O,EAAAC,EAAAC,CAAA,EACAnkB,EAAA,EAAAA,EAAAmD,KAAAwhB,aAAA5lB,OAAA,EAAAiB,EAAA,CACA,IAAA6kB,EAAA7mB,EAAAqhB,SAAAX,EAAAvb,KAAAshB,EAAAzkB,IAAAZ,QAAA,EAAAxB,IAAA,EAAA6E,QAAA,WAAA,EAAA,EACAmiB,EAAAC,GAAA7mB,EAAAqD,QAAA,CAAA,IAAA,KAAArD,EAAA8mB,WAAAD,CAAA,EAAAA,EAAA,IAAAA,CAAA,EAAA,gCAAA,EAAA,CACAE,EAAArG,EACAsG,EAAAtG,EAAA3H,oBAAApD,KACAsR,EAAAvG,EAAA1H,qBAAArD,IACA,CAAA,CACA,CACA,OAAAiR,CACA,C,iDC3LArmB,EAAAR,QAAA+X,EAEA,IAAAoP,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACAjmB,EAAA,KACAU,EAAA,IACA,EASA,SAAAwlB,EAAAhI,GACA,OAAAA,EAAApb,QAAAgjB,EAAA,SAAA/iB,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAA+iB,EAAA/iB,IAAA,EACA,CACA,CAAA,CACA,CA6DA,SAAAmT,EAAAnU,EAAA0a,GAEA1a,EAAAA,EAAAC,SAAA,EAEA,IAAA5C,EAAA,EACAD,EAAA4C,EAAA5C,OACAke,EAAA,EACA6I,EAAA,EACAzV,EAAA,GAEA0V,EAAA,GAEAC,EAAA,KASA,SAAAjJ,EAAAkJ,GACA,OAAA9kB,MAAA,WAAA8kB,EAAA,UAAAhJ,EAAA,GAAA,CACA,CAyBA,SAAAiJ,EAAA3gB,GACA,OAAA5D,EAAAA,EAAA4D,IAAA5D,EACA,CAUA,SAAAwkB,EAAAhmB,EAAAC,EAAAgmB,GACA,IAYAnlB,EAZAmP,EAAA,CACA7F,KAAA5I,EAAAA,EAAAxB,CAAA,KAAAwB,GACA0kB,UAAA,CAAA,EACAC,QAAAF,CACA,EAGAG,EADAlK,EACA,EAEA,EAEAmK,EAAArmB,EAAAomB,EAEA,GACA,GAAA,EAAAC,EAAA,GACA,OAAAvlB,EAAAU,EAAAA,EAAA6kB,IAAA7kB,IAAA,CACAyO,EAAAiW,UAAA,CAAA,EACA,KACA,CAAA,OACA,MAAAplB,GAAA,OAAAA,GAIA,IAHA,IAAAwlB,EAAA9kB,EACA2b,UAAAnd,EAAAC,CAAA,EACAyI,MAAA0c,CAAA,EACAvlB,EAAA,EAAAA,EAAAymB,EAAA1nB,OAAA,EAAAiB,EACAymB,EAAAzmB,GAAAymB,EAAAzmB,GACAyC,QAAA4Z,EAAAiJ,EAAAD,EAAA,EAAA,EACAqB,KAAA,EACAtW,EAAAuW,KAAAF,EACA3lB,KAAA,IAAA,EACA4lB,KAAA,EAEArW,EAAA4M,GAAA7M,EACA0V,EAAA7I,CACA,CAEA,SAAA2J,EAAAC,GACA,IAAAC,EAAAC,EAAAF,CAAA,EAGAG,EAAArlB,EAAA2b,UAAAuJ,EAAAC,CAAA,EAEA,MADA,WAAA1lB,KAAA4lB,CAAA,CAEA,CAEA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAA/nB,GAAA,OAAAmnB,EAAAY,CAAA,GACAA,CAAA,GAEA,OAAAA,CACA,CAOA,SAAAxK,IACA,GAAA,EAAAyJ,EAAAhnB,OACA,OAAAgnB,EAAA/c,MAAA,EACA,GAAAgd,EAAA,CA3FA,IAAAkB,EAAA,MAAAlB,EAAAZ,EAAAD,EAEAgC,GADAD,EAAAE,UAAApoB,EAAA,EACAkoB,EAAAG,KAAA1lB,CAAA,GACA,GAAAwlB,EAKA,OAHAnoB,EAAAkoB,EAAAE,UACA1mB,EAAAslB,CAAA,EACAA,EAAA,KACAH,EAAAsB,EAAA,EAAA,EAJA,MAAApK,EAAA,QAAA,CAwFA,CACA,IAAAuK,EACApP,EACAqP,EACApnB,EACAqnB,EACAC,EAAA,IAAAzoB,EACA,EAAA,CACA,GAAAA,IAAAD,EACA,OAAA,KAEA,IADAuoB,EAAA,CAAA,EACA9B,EAAApkB,KAAAmmB,EAAArB,EAAAlnB,CAAA,CAAA,GAKA,GAJA,OAAAuoB,IACAE,EAAA,CAAA,EACA,EAAAxK,GAEA,EAAAje,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAmnB,EAAAlnB,CAAA,EAAA,CACA,GAAA,EAAAA,IAAAD,EACA,MAAAge,EAAA,SAAA,EAEA,GAAA,MAAAmJ,EAAAlnB,CAAA,EACA,GAAAqd,EAAA,CAsBA,GADAmL,EAAA,CAAA,EACAZ,GAFAzmB,EAAAnB,GAEA,CAAA,EAEA,IADAwoB,EAAA,CAAA,GAEAxoB,EAAA+nB,EAAA/nB,CAAA,KACAD,IAGAC,CAAA,GACAyoB,GAIAb,EAAA5nB,CAAA,UAEAA,EAAAY,KAAA2iB,IAAAxjB,EAAAgoB,EAAA/nB,CAAA,EAAA,CAAA,EAEAwoB,IACArB,EAAAhmB,EAAAnB,EAAAyoB,CAAA,EACAA,EAAA,CAAA,GAEAxK,CAAA,EAEA,KA5CA,CAIA,IAFAuK,EAAA,MAAAtB,EAAA/lB,EAAAnB,EAAA,CAAA,EAEA,OAAAknB,EAAA,EAAAlnB,CAAA,GACA,GAAAA,IAAAD,EACA,OAAA,KAGA,EAAAC,EACAwoB,IACArB,EAAAhmB,EAAAnB,EAAA,EAAAyoB,CAAA,EAGAA,EAAA,CAAA,GAEA,EAAAxK,CA4BA,KA7CA,CA8CA,GAAA,OAAAsK,EAAArB,EAAAlnB,CAAA,GAqBA,MAAA,IAnBAmB,EAAAnB,EAAA,EACAwoB,EAAAnL,GAAA,MAAA6J,EAAA/lB,CAAA,EACA,GAIA,GAHA,OAAAonB,GACA,EAAAtK,EAEA,EAAAje,IAAAD,EACA,MAAAge,EAAA,SAAA,CACA,OACA7E,EAAAqP,EACAA,EAAArB,EAAAlnB,CAAA,EACA,MAAAkZ,GAAA,MAAAqP,GACA,EAAAvoB,EACAwoB,IACArB,EAAAhmB,EAAAnB,EAAA,EAAAyoB,CAAA,EACAA,EAAA,CAAA,EAKA,CAxBAH,EAAA,CAAA,CAyBA,CACA,OAAAA,GAIA,IAAAlnB,EAAApB,EAGA,GAFAkmB,EAAAkC,UAAA,EAEA,CADAlC,EAAA9jB,KAAA8kB,EAAA9lB,CAAA,EAAA,CAAA,EAEA,KAAAA,EAAArB,GAAA,CAAAmmB,EAAA9jB,KAAA8kB,EAAA9lB,CAAA,CAAA,GACA,EAAAA,EACA6b,EAAAta,EAAA2b,UAAAte,EAAAA,EAAAoB,CAAA,EAGA,MAFA,KAAA6b,GAAA,KAAAA,IACA+J,EAAA/J,GACAA,CACA,CAQA,SAAAvb,EAAAub,GACA8J,EAAArlB,KAAAub,CAAA,CACA,CAOA,SAAAM,IACA,GAAA,CAAAwJ,EAAAhnB,OAAA,CACA,IAAAkd,EAAAK,EAAA,EACA,GAAA,OAAAL,EACA,OAAA,KACAvb,EAAAub,CAAA,CACA,CACA,OAAA8J,EAAA,EACA,CAmDA,OAAA9jB,OAAA2Q,eAAA,CACA0J,KAAAA,EACAC,KAAAA,EACA7b,KAAAA,EACA8b,KA7CA,SAAAkL,EAAA5X,GACA,IAAA6X,EAAApL,EAAA,EAEA,GADAoL,IAAAD,EAGA,OADApL,EAAA,EACA,CAAA,EAEA,GAAAxM,EAEA,MAAA,CAAA,EADA,MAAAiN,EAAA,UAAA4K,EAAA,OAAAD,EAAA,YAAA,CAEA,EAoCAjL,KA5BA,SAAAuC,GACA,IACA5O,EADAwX,EAAA,KAmBA,OAjBA5I,IAAA1hB,IACA8S,EAAAC,EAAA4M,EAAA,GACA,OAAA5M,EAAA4M,EAAA,GACA7M,IAAAiM,GAAA,MAAAjM,EAAA7F,MAAA6F,EAAAiW,aACAuB,EAAAxX,EAAAkW,QAAAlW,EAAAuW,KAAA,QAIAb,EAAA9G,GACAzC,EAAA,EAEAnM,EAAAC,EAAA2O,GACA,OAAA3O,EAAA2O,GACA5O,CAAAA,GAAAA,EAAAiW,WAAAhK,CAAAA,GAAA,MAAAjM,EAAA7F,OACAqd,EAAAxX,EAAAkW,QAAA,KAAAlW,EAAAuW,OAGAiB,CACA,CAQA,EAAA,OAAA,CACAjb,IAAA,WAAA,OAAAsQ,CAAA,CACA,CAAA,CAEA,CAxXAnH,EAAA+P,SAAAA,C,0BCtCAtnB,EAAAR,QAAAoU,EAGA,IAAAnC,EAAAvR,EAAA,EAAA,EAGAoO,KAFAsF,EAAA9O,UAAApB,OAAAgO,OAAAD,EAAA3M,SAAA,GAAA6M,YAAAiC,GAAAhC,UAAA,OAEA1R,EAAA,EAAA,GACAyW,EAAAzW,EAAA,EAAA,EACAyT,EAAAzT,EAAA,EAAA,EACA0W,EAAA1W,EAAA,EAAA,EACA2W,EAAA3W,EAAA,EAAA,EACA6W,EAAA7W,EAAA,EAAA,EACAiX,EAAAjX,EAAA,EAAA,EACA+W,EAAA/W,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EACAsW,EAAAtW,EAAA,EAAA,EACAuW,EAAAvW,EAAA,EAAA,EACAwW,EAAAxW,EAAA,EAAA,EACAiP,EAAAjP,EAAA,EAAA,EACA8W,EAAA9W,EAAA,EAAA,EAUA,SAAA0T,EAAAvU,EAAAqG,GACA+L,EAAAlS,KAAAqF,KAAAvF,EAAAqG,CAAA,EAMAd,KAAAkH,OAAA,GAMAlH,KAAA+H,OAAA5N,GAMA6F,KAAAgc,WAAA7hB,GAMA6F,KAAAsN,SAAAnT,GAMA6F,KAAA2Q,MAAAxW,GAOA6F,KAAA0kB,EAAA,KAOA1kB,KAAA6L,EAAA,KAOA7L,KAAA2kB,EAAA,KAOA3kB,KAAA4kB,EAAA,IACA,CAyHA,SAAAtQ,EAAAlN,GAKA,OAJAA,EAAAsd,EAAAtd,EAAAyE,EAAAzE,EAAAud,EAAA,KACA,OAAAvd,EAAAtK,OACA,OAAAsK,EAAAvJ,OACA,OAAAuJ,EAAAkM,OACAlM,CACA,CA7HAtI,OAAA+X,iBAAA7H,EAAA9O,UAAA,CAQA2kB,WAAA,CACArb,IAAA,WAGA,GAAAxJ,CAAAA,KAAA0kB,EAAA,CAGA1kB,KAAA0kB,EAAA,GACA,IAAA,IAAA9P,EAAA9V,OAAAC,KAAAiB,KAAAkH,MAAA,EAAArK,EAAA,EAAAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EAAA,CACA,IAAAgN,EAAA7J,KAAAkH,OAAA0N,EAAA/X,IACAwK,EAAAwC,EAAAxC,GAGA,GAAArH,KAAA0kB,EAAArd,GACA,MAAArJ,MAAA,gBAAAqJ,EAAA,OAAArH,IAAA,EAEAA,KAAA0kB,EAAArd,GAAAwC,CACA,CAZA,CAaA,OAAA7J,KAAA0kB,CACA,CACA,EAQAha,YAAA,CACAlB,IAAA,WACA,OAAAxJ,KAAA6L,IAAA7L,KAAA6L,EAAAhR,EAAA4Z,QAAAzU,KAAAkH,MAAA,EACA,CACA,EAQA4d,YAAA,CACAtb,IAAA,WACA,OAAAxJ,KAAA2kB,IAAA3kB,KAAA2kB,EAAA9pB,EAAA4Z,QAAAzU,KAAA+H,MAAA,EACA,CACA,EAQAyI,KAAA,CACAhH,IAAA,WACA,OAAAxJ,KAAA4kB,IAAA5kB,KAAAwQ,KAAAxB,EAAA+V,oBAAA/kB,IAAA,EAAA,EACA,EACA+X,IAAA,SAAAvH,GAmBA,IAhBA,IAAAtQ,EAAAsQ,EAAAtQ,UAeArD,GAdAqD,aAAAiS,KACA3B,EAAAtQ,UAAA,IAAAiS,GAAApF,YAAAyD,EACA3V,EAAA+jB,MAAApO,EAAAtQ,UAAAA,CAAA,GAIAsQ,EAAAyC,MAAAzC,EAAAtQ,UAAA+S,MAAAjT,KAGAnF,EAAA+jB,MAAApO,EAAA2B,EAAA,CAAA,CAAA,EAEAnS,KAAA4kB,EAAApU,EAGA,GACA3T,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,EACAmD,KAAA6L,EAAAhP,GAAAZ,QAAA,EAIA,IADA,IAAA+oB,EAAA,GACAnoB,EAAA,EAAAA,EAAAmD,KAAA8kB,YAAAlpB,OAAA,EAAAiB,EACAmoB,EAAAhlB,KAAA2kB,EAAA9nB,GAAAZ,QAAA,EAAAxB,MAAA,CACA+O,IAAA3O,EAAAid,YAAA9X,KAAA2kB,EAAA9nB,GAAAoL,KAAA,EACA8P,IAAAld,EAAAmd,YAAAhY,KAAA2kB,EAAA9nB,GAAAoL,KAAA,CACA,EACApL,GACAiC,OAAA+X,iBAAArG,EAAAtQ,UAAA8kB,CAAA,CACA,CACA,CACA,CAAA,EAOAhW,EAAA+V,oBAAA,SAAAta,GAIA,IAFA,IAEAZ,EAFAD,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,IAAA,EAEAoC,EAAA,EAAAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,GACAgN,EAAAY,EAAAoB,EAAAhP,IAAA+N,IAAAhB,EACA,YAAA/O,EAAA8P,SAAAd,EAAApP,IAAA,CAAA,EACAoP,EAAAM,UAAAP,EACA,YAAA/O,EAAA8P,SAAAd,EAAApP,IAAA,CAAA,EACA,OAAAmP,EACA,uEAAA,EACA,sBAAA,CAEA,EA2BAoF,EAAAhB,SAAA,SAAAvT,EAAAqM,GAMA,IALA,IAAAM,EAAA,IAAA4H,EAAAvU,EAAAqM,EAAAhG,OAAA,EAGA8T,GAFAxN,EAAA4U,WAAAlV,EAAAkV,WACA5U,EAAAkG,SAAAxG,EAAAwG,SACAxO,OAAAC,KAAA+H,EAAAI,MAAA,GACArK,EAAA,EACAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EACAuK,EAAAoH,KACA,KAAA,IAAA1H,EAAAI,OAAA0N,EAAA/X,IAAAgL,QACAmK,EACAjD,GADAf,SACA4G,EAAA/X,GAAAiK,EAAAI,OAAA0N,EAAA/X,GAAA,CACA,EACA,GAAAiK,EAAAiB,OACA,IAAA6M,EAAA9V,OAAAC,KAAA+H,EAAAiB,MAAA,EAAAlL,EAAA,EAAAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EACAuK,EAAAoH,IAAAuD,EAAA/D,SAAA4G,EAAA/X,GAAAiK,EAAAiB,OAAA6M,EAAA/X,GAAA,CAAA,EACA,GAAAiK,EAAAC,OACA,IAAA6N,EAAA9V,OAAAC,KAAA+H,EAAAC,MAAA,EAAAlK,EAAA,EAAAA,EAAA+X,EAAAhZ,OAAA,EAAAiB,EAAA,CACA,IAAAkK,EAAAD,EAAAC,OAAA6N,EAAA/X,IACAuK,EAAAoH,KACAzH,EAAAM,KAAAlN,GACA4U,EACAhI,EAAAG,SAAA/M,GACA6U,EACAjI,EAAA0B,SAAAtO,GACAuP,EACA3C,EAAA8N,UAAA1a,GACA8X,EACApF,GAPAmB,SAOA4G,EAAA/X,GAAAkK,CAAA,CACA,CACA,CAYA,OAXAD,EAAAkV,YAAAlV,EAAAkV,WAAApgB,SACAwL,EAAA4U,WAAAlV,EAAAkV,YACAlV,EAAAwG,UAAAxG,EAAAwG,SAAA1R,SACAwL,EAAAkG,SAAAxG,EAAAwG,UACAxG,EAAA6J,QACAvJ,EAAAuJ,MAAA,CAAA,GACA7J,EAAAmG,UACA7F,EAAA6F,QAAAnG,EAAAmG,SACAnG,EAAA0G,UACApG,EAAAqG,EAAA3G,EAAA0G,SACApG,EAAA8G,EAAA,SACA9G,CACA,EAOA4H,EAAA9O,UAAAiO,OAAA,SAAAC,GACA,IAAAmT,EAAA1U,EAAA3M,UAAAiO,OAAAxT,KAAAqF,KAAAoO,CAAA,EACAC,EAAAD,CAAAA,CAAAA,GAAAE,CAAAA,CAAAF,EAAAC,aACA,OAAAxT,EAAAgQ,SAAA,CACA,UAAA7K,KAAAuO,EAAA,EACA,UAAAgT,GAAAA,EAAAzgB,SAAA3G,GACA,SAAA0S,EAAAkH,YAAA/T,KAAA8kB,YAAA1W,CAAA,EACA,SAAAvB,EAAAkH,YAAA/T,KAAA0K,YAAAqB,OAAA,SAAAkI,GAAA,MAAA,CAAAA,EAAAzE,cAAA,CAAA,EAAApB,CAAA,GAAA,GACA,aAAApO,KAAAgc,YAAAhc,KAAAgc,WAAApgB,OAAAoE,KAAAgc,WAAA7hB,GACA,WAAA6F,KAAAsN,UAAAtN,KAAAsN,SAAA1R,OAAAoE,KAAAsN,SAAAnT,GACA,QAAA6F,KAAA2Q,OAAAxW,GACA,SAAAonB,GAAAA,EAAAxa,QAAA5M,GACA,UAAAkU,EAAArO,KAAAiN,QAAA9S,GACA,CACA,EAKA6U,EAAA9O,UAAAoV,WAAA,WACA,GAAAtV,KAAAqU,EAAA,CAEAxH,EAAA3M,UAAAoV,WAAA3a,KAAAqF,IAAA,EAEA,IADA,IAAA+H,EAAA/H,KAAA8kB,YAAAjoB,EAAA,EACAA,EAAAkL,EAAAnM,QACAmM,EAAAlL,CAAA,IAAAZ,QAAA,EAEA,IADA,IAAAiL,EAAAlH,KAAA0K,YAAA7N,EAAA,EACAA,EAAAqK,EAAAtL,QACAsL,EAAArK,CAAA,IAAAZ,QAAA,CARA,CASA,OAAA+D,IACA,EAKAgP,EAAA9O,UAAAqV,EAAA,SAAA/H,GAYA,OAXAxN,KAAAoU,IAEA5G,EAAAxN,KAAAyN,GAAAD,EAEAX,EAAA3M,UAAAqV,EAAA5a,KAAAqF,KAAAwN,CAAA,EACAxN,KAAA8kB,YAAApX,QAAAzF,IACAA,EAAAsF,EAAAC,CAAA,CACA,CAAA,EACAxN,KAAA0K,YAAAgD,QAAA7D,IACAA,EAAA0D,EAAAC,CAAA,CACA,CAAA,GACAxN,IACA,EAKAgP,EAAA9O,UAAAsJ,IAAA,SAAA/O,GACA,OAAAuF,KAAAkH,OAAAzM,IACAuF,KAAA+H,QAAA/H,KAAA+H,OAAAtN,IACAuF,KAAA+G,QAAA/G,KAAA+G,OAAAtM,IACA,IACA,EASAuU,EAAA9O,UAAAsO,IAAA,SAAA+E,GAEA,GAAAvT,KAAAwJ,IAAA+J,EAAA9Y,IAAA,EACA,MAAAuD,MAAA,mBAAAuV,EAAA9Y,KAAA,QAAAuF,IAAA,EAEA,GAAAuT,aAAAxE,GAAAwE,EAAArE,SAAA/U,GAAA,CAMA,IAAA6F,KAAA0kB,GAAA1kB,KAAA6kB,YAAAtR,EAAAlM,IACA,MAAArJ,MAAA,gBAAAuV,EAAAlM,GAAA,OAAArH,IAAA,EACA,GAAAA,KAAA2O,aAAA4E,EAAAlM,EAAA,EACA,MAAArJ,MAAA,MAAAuV,EAAAlM,GAAA,mBAAArH,IAAA,EACA,GAAAA,KAAA4O,eAAA2E,EAAA9Y,IAAA,EACA,MAAAuD,MAAA,SAAAuV,EAAA9Y,KAAA,oBAAAuF,IAAA,EAOA,OALAuT,EAAAvD,QACAuD,EAAAvD,OAAAlB,OAAAyE,CAAA,GACAvT,KAAAkH,OAAAqM,EAAA9Y,MAAA8Y,GACAlE,QAAArP,KACAuT,EAAA0B,MAAAjV,IAAA,EACAsU,EAAAtU,IAAA,CACA,CACA,OAAAuT,aAAAxB,GACA/R,KAAA+H,SACA/H,KAAA+H,OAAA,KACA/H,KAAA+H,OAAAwL,EAAA9Y,MAAA8Y,GACA0B,MAAAjV,IAAA,EACAsU,EAAAtU,IAAA,GAEA6M,EAAA3M,UAAAsO,IAAA7T,KAAAqF,KAAAuT,CAAA,CACA,EASAvE,EAAA9O,UAAA4O,OAAA,SAAAyE,GACA,GAAAA,aAAAxE,GAAAwE,EAAArE,SAAA/U,GAAA,CAIA,GAAA6F,KAAAkH,QAAAlH,KAAAkH,OAAAqM,EAAA9Y,QAAA8Y,EAMA,OAHA,OAAAvT,KAAAkH,OAAAqM,EAAA9Y,MACA8Y,EAAAvD,OAAA,KACAuD,EAAA2B,SAAAlV,IAAA,EACAsU,EAAAtU,IAAA,EALA,MAAAhC,MAAAuV,EAAA,uBAAAvT,IAAA,CAMA,CACA,GAAAuT,aAAAxB,EAAA,CAGA,GAAA/R,KAAA+H,QAAA/H,KAAA+H,OAAAwL,EAAA9Y,QAAA8Y,EAMA,OAHA,OAAAvT,KAAA+H,OAAAwL,EAAA9Y,MACA8Y,EAAAvD,OAAA,KACAuD,EAAA2B,SAAAlV,IAAA,EACAsU,EAAAtU,IAAA,EALA,MAAAhC,MAAAuV,EAAA,uBAAAvT,IAAA,CAMA,CACA,OAAA6M,EAAA3M,UAAA4O,OAAAnU,KAAAqF,KAAAuT,CAAA,CACA,EAOAvE,EAAA9O,UAAAyO,aAAA,SAAAtH,GACA,OAAAwF,EAAA8B,aAAA3O,KAAAsN,SAAAjG,CAAA,CACA,EAOA2H,EAAA9O,UAAA0O,eAAA,SAAAnU,GACA,OAAAoS,EAAA+B,eAAA5O,KAAAsN,SAAA7S,CAAA,CACA,EAOAuU,EAAA9O,UAAA4M,OAAA,SAAAkG,GACA,OAAA,IAAAhT,KAAAwQ,KAAAwC,CAAA,CACA,EAMAhE,EAAA9O,UAAA+kB,MAAA,WAMA,IAFA,IAAA7a,EAAApK,KAAAoK,SACA6B,EAAA,GACApP,EAAA,EAAAA,EAAAmD,KAAA0K,YAAA9O,OAAA,EAAAiB,EACAoP,EAAA1O,KAAAyC,KAAA6L,EAAAhP,GAAAZ,QAAA,EAAAgO,YAAA,EAGAjK,KAAAlD,OAAA8U,EAAA5R,IAAA,EAAA,CACAqS,OAAAA,EACApG,MAAAA,EACApR,KAAAA,CACA,CAAA,EACAmF,KAAAnC,OAAAgU,EAAA7R,IAAA,EAAA,CACAuS,OAAAA,EACAtG,MAAAA,EACApR,KAAAA,CACA,CAAA,EACAmF,KAAAsT,OAAAxB,EAAA9R,IAAA,EAAA,CACAiM,MAAAA,EACApR,KAAAA,CACA,CAAA,EACAmF,KAAAwK,WAAAD,EAAAC,WAAAxK,IAAA,EAAA,CACAiM,MAAAA,EACApR,KAAAA,CACA,CAAA,EACAmF,KAAA6K,SAAAN,EAAAM,SAAA7K,IAAA,EAAA,CACAiM,MAAAA,EACApR,KAAAA,CACA,CAAA,EAGA,IAEAqqB,EAFAC,EAAA/S,EAAAhI,GAaA,OAZA+a,KACAD,EAAApmB,OAAAgO,OAAA9M,IAAA,GAEAwK,WAAAxK,KAAAwK,WACAxK,KAAAwK,WAAA2a,EAAA3a,WAAAhG,KAAA0gB,CAAA,EAGAA,EAAAra,SAAA7K,KAAA6K,SACA7K,KAAA6K,SAAAsa,EAAAta,SAAArG,KAAA0gB,CAAA,GAIAllB,IACA,EAQAgP,EAAA9O,UAAApD,OAAA,SAAAuS,EAAA6D,GACA,OAAAlT,KAAAilB,MAAA,EAAAnoB,OAAAuS,EAAA6D,CAAA,CACA,EAQAlE,EAAA9O,UAAAiT,gBAAA,SAAA9D,EAAA6D,GACA,OAAAlT,KAAAlD,OAAAuS,EAAA6D,GAAAA,EAAA3M,IAAA2M,EAAAkS,KAAA,EAAAlS,CAAA,EAAAmS,OAAA,CACA,EAUArW,EAAA9O,UAAArC,OAAA,SAAAuV,EAAAxX,GACA,OAAAoE,KAAAilB,MAAA,EAAApnB,OAAAuV,EAAAxX,CAAA,CACA,EASAoT,EAAA9O,UAAAmT,gBAAA,SAAAD,GAGA,OAFAA,aAAAb,IACAa,EAAAb,EAAAzF,OAAAsG,CAAA,GACApT,KAAAnC,OAAAuV,EAAAA,EAAA6K,OAAA,CAAA,CACA,EAOAjP,EAAA9O,UAAAoT,OAAA,SAAAjE,GACA,OAAArP,KAAAilB,MAAA,EAAA3R,OAAAjE,CAAA,CACA,EAOAL,EAAA9O,UAAAsK,WAAA,SAAA+I,GACA,OAAAvT,KAAAilB,MAAA,EAAAza,WAAA+I,CAAA,CACA,EA2BAvE,EAAA9O,UAAA2K,SAAA,SAAAwE,EAAAvO,GACA,OAAAd,KAAAilB,MAAA,EAAApa,SAAAwE,EAAAvO,CAAA,CACA,EAiBAkO,EAAA6B,EAAA,SAAAyU,GACA,OAAA,SAAA/K,GACA1f,EAAAoW,aAAAsJ,EAAA+K,CAAA,CACA,CACA,C,mHC/lBA,IAEAzqB,EAAAS,EAAA,EAAA,EAEAwmB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAyD,EAAA9c,EAAA5M,GACA,IAAAgB,EAAA,EAAA2oB,EAAA,GAEA,IADA3pB,GAAA,EACAgB,EAAA4L,EAAA7M,QAAA4pB,EAAA1D,EAAAjlB,EAAAhB,IAAA4M,EAAA5L,CAAA,IACA,OAAA2oB,CACA,CAsBAvZ,EAAAE,MAAAoZ,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EAuBAtZ,EAAAC,SAAAqZ,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,CAAA,EACA,GACA1qB,EAAA0V,WACA,KACA,EAYAtE,EAAAX,KAAAia,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,CAAA,EAmBAtZ,EAAAQ,OAAA8Y,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,CAAA,EAoBAtZ,EAAAG,OAAAmZ,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,C,+BC7LA,IAIAvW,EACAtF,EALA7O,EAAAO,EAAAR,QAAAU,EAAA,EAAA,EAEAoX,EAAApX,EAAA,EAAA,EAiDAmqB,GA5CA5qB,EAAAqD,QAAA5C,EAAA,CAAA,EACAT,EAAA6F,MAAApF,EAAA,CAAA,EACAT,EAAA2K,KAAAlK,EAAA,CAAA,EAMAT,EAAA+F,GAAA/F,EAAAqK,QAAA,IAAA,EAOArK,EAAA4Z,QAAA,SAAAlB,GACA,GAAAA,EAAA,CAIA,IAHA,IAAAxU,EAAAD,OAAAC,KAAAwU,CAAA,EACAS,EAAAtY,MAAAqD,EAAAnD,MAAA,EACAE,EAAA,EACAA,EAAAiD,EAAAnD,QACAoY,EAAAlY,GAAAyX,EAAAxU,EAAAjD,CAAA,KACA,OAAAkY,CACA,CACA,MAAA,EACA,EAOAnZ,EAAAgQ,SAAA,SAAAmJ,GAGA,IAFA,IAAAT,EAAA,GACAzX,EAAA,EACAA,EAAAkY,EAAApY,QAAA,CACA,IAAA+R,EAAAqG,EAAAlY,CAAA,IACAoG,EAAA8R,EAAAlY,CAAA,IACAoG,IAAA/H,KACAoZ,EAAA5F,GAAAzL,EACA,CACA,OAAAqR,CACA,EAEA,OACAmS,EAAA,KA+BAC,GAxBA9qB,EAAA8mB,WAAA,SAAAlnB,GACA,MAAA,uTAAAwD,KAAAxD,CAAA,CACA,EAOAI,EAAA8P,SAAA,SAAAZ,GACA,MAAA,CAAA,YAAA9L,KAAA8L,CAAA,GAAAlP,EAAA8mB,WAAA5X,CAAA,EACA,KAAAA,EAAAzK,QAAAmmB,EAAA,MAAA,EAAAnmB,QAAAomB,EAAA,KAAA,EAAA,KACA,IAAA3b,CACA,EAOAlP,EAAAshB,QAAA,SAAAzB,GACA,OAAAA,EAAA,IAAAA,IAAAkL,YAAA,EAAAlL,EAAAP,UAAA,CAAA,CACA,EAEA,aAuDA0L,GAhDAhrB,EAAA8e,UAAA,SAAAe,GACA,OAAAA,EAAAP,UAAA,EAAA,CAAA,EACAO,EAAAP,UAAA,CAAA,EACA7a,QAAAqmB,EAAA,SAAApmB,EAAAC,GAAA,OAAAA,EAAAomB,YAAA,CAAA,CAAA,CACA,EAQA/qB,EAAAkQ,kBAAA,SAAA+a,EAAAxoB,GACA,OAAAwoB,EAAAze,GAAA/J,EAAA+J,EACA,EAUAxM,EAAAoW,aAAA,SAAAT,EAAA8U,GAGA,OAAA9U,EAAAyC,OACAqS,GAAA9U,EAAAyC,MAAAxY,OAAA6qB,IACAzqB,EAAAkrB,aAAAjX,OAAA0B,EAAAyC,KAAA,EACAzC,EAAAyC,MAAAxY,KAAA6qB,EACAzqB,EAAAkrB,aAAAvX,IAAAgC,EAAAyC,KAAA,GAEAzC,EAAAyC,QAOA7L,EAAA,IAFA4H,EADAA,GACA1T,EAAA,EAAA,GAEAgqB,GAAA9U,EAAA/V,IAAA,EACAI,EAAAkrB,aAAAvX,IAAApH,CAAA,EACAA,EAAAoJ,KAAAA,EACA1R,OAAA2Q,eAAAe,EAAA,QAAA,CAAA/Q,MAAA2H,EAAA4e,WAAA,CAAA,CAAA,CAAA,EACAlnB,OAAA2Q,eAAAe,EAAAtQ,UAAA,QAAA,CAAAT,MAAA2H,EAAA4e,WAAA,CAAA,CAAA,CAAA,EACA5e,EACA,EAEA,GAOAvM,EAAAqW,aAAA,SAAAqC,GAGA,IAOAtF,EAPA,OAAAsF,EAAAN,QAOAhF,EAAA,IAFAvE,EADAA,GACApO,EAAA,EAAA,GAEA,OAAAuqB,CAAA,GAAAtS,CAAA,EACA1Y,EAAAkrB,aAAAvX,IAAAP,CAAA,EACAnP,OAAA2Q,eAAA8D,EAAA,QAAA,CAAA9T,MAAAwO,EAAA+X,WAAA,CAAA,CAAA,CAAA,EACA/X,EACA,EAWApT,EAAAsc,YAAA,SAAA8O,EAAAzgB,EAAA/F,EAAAqQ,GAmBA,GAAA,UAAA,OAAAmW,EACA,MAAA7Y,UAAA,uBAAA,EACA,GAAA5H,EAIA,OAxBA,SAAA0gB,EAAAD,EAAAzgB,EAAA/F,GACA,IAAA4V,EAAA7P,EAAAK,MAAA,EACA,GAAA,cAAAwP,GAAA,cAAAA,EAGA,GAAA,EAAA7P,EAAA5J,OACAqqB,EAAA5Q,GAAA6Q,EAAAD,EAAA5Q,IAAA,GAAA7P,EAAA/F,CAAA,MACA,CAEA,IADAwd,EAAAgJ,EAAA5Q,KACAvF,EACA,OAAAmW,EACAhJ,IACAxd,EAAA,GAAAmd,OAAAK,CAAA,EAAAL,OAAAnd,CAAA,GACAwmB,EAAA5Q,GAAA5V,CACA,CACA,OAAAwmB,CACA,EAQAA,EADAzgB,EAAAA,EAAAE,MAAA,GAAA,EACAjG,CAAA,EAHA,MAAA2N,UAAA,wBAAA,CAIA,EAQAtO,OAAA2Q,eAAA5U,EAAA,eAAA,CACA2O,IAAA,WACA,OAAAkJ,EAAA,YAAAA,EAAA,UAAA,IAAApX,EAAA,EAAA,GACA,CACA,CAAA,C,mECrNAF,EAAAR,QAAAwiB,EAEA,IAAAviB,EAAAS,EAAA,EAAA,EAUA,SAAA8hB,EAAAvZ,EAAAC,GASA9D,KAAA6D,GAAAA,IAAA,EAMA7D,KAAA8D,GAAAA,IAAA,CACA,CAOA,IAAAqiB,EAAA/I,EAAA+I,KAAA,IAAA/I,EAAA,EAAA,CAAA,EAoFArf,GAlFAooB,EAAAza,SAAA,WAAA,OAAA,CAAA,EACAya,EAAAC,SAAAD,EAAAnH,SAAA,WAAA,OAAAhf,IAAA,EACAmmB,EAAAvqB,OAAA,WAAA,OAAA,CAAA,EAOAwhB,EAAAiJ,SAAA,mBAOAjJ,EAAAjN,WAAA,SAAA1Q,GACA,IAEA4C,EAGAwB,EALA,OAAA,IAAApE,EACA0mB,GAIAtiB,GADApE,GAFA4C,EAAA5C,EAAA,GAEA,CAAAA,EACAA,KAAA,EACAqE,GAAArE,EAAAoE,GAAA,aAAA,EACAxB,IACAyB,EAAA,CAAAA,IAAA,EACAD,EAAA,CAAAA,IAAA,EACA,WAAA,EAAAA,IACAA,EAAA,EACA,WAAA,EAAAC,IACAA,EAAA,KAGA,IAAAsZ,EAAAvZ,EAAAC,CAAA,EACA,EAOAsZ,EAAAkJ,KAAA,SAAA7mB,GACA,GAAA,UAAA,OAAAA,EACA,OAAA2d,EAAAjN,WAAA1Q,CAAA,EACA,GAAA5E,EAAA4T,SAAAhP,CAAA,EAAA,CAEA,GAAA5E,CAAAA,EAAAI,KAGA,OAAAmiB,EAAAjN,WAAAiK,SAAA3a,EAAA,EAAA,CAAA,EAFAA,EAAA5E,EAAAI,KAAAsrB,WAAA9mB,CAAA,CAGA,CACA,OAAAA,EAAA8L,KAAA9L,EAAA+L,KAAA,IAAA4R,EAAA3d,EAAA8L,MAAA,EAAA9L,EAAA+L,OAAA,CAAA,EAAA2a,CACA,EAOA/I,EAAAld,UAAAwL,SAAA,SAAAD,GACA,IAEA3H,EAFA,MAAA,CAAA2H,GAAAzL,KAAA8D,KAAA,IACAD,EAAA,EAAA,CAAA7D,KAAA6D,KAAA,EACAC,EAAA,CAAA9D,KAAA8D,KAAA,EAGA,EAAAD,EAAA,YADAC,EADAD,EAEAC,EADAA,EAAA,IAAA,KAGA9D,KAAA6D,GAAA,WAAA7D,KAAA8D,EACA,EAOAsZ,EAAAld,UAAAsmB,OAAA,SAAA/a,GACA,OAAA5Q,EAAAI,KACA,IAAAJ,EAAAI,KAAA,EAAA+E,KAAA6D,GAAA,EAAA7D,KAAA8D,GAAAwK,CAAAA,CAAA7C,CAAA,EAEA,CAAAF,IAAA,EAAAvL,KAAA6D,GAAA2H,KAAA,EAAAxL,KAAA8D,GAAA2H,SAAA6C,CAAAA,CAAA7C,CAAA,CACA,EAEAjO,OAAA0C,UAAAnC,YAOAqf,EAAAqJ,SAAA,SAAAC,GACA,MAjFAtJ,qBAiFAsJ,EACAP,EACA,IAAA/I,GACArf,EAAApD,KAAA+rB,EAAA,CAAA,EACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,EACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,GACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,MAAA,GAEA3oB,EAAApD,KAAA+rB,EAAA,CAAA,EACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,EACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,GACA3oB,EAAApD,KAAA+rB,EAAA,CAAA,GAAA,MAAA,CACA,CACA,EAMAtJ,EAAAld,UAAAymB,OAAA,WACA,OAAAnpB,OAAAC,aACA,IAAAuC,KAAA6D,GACA7D,KAAA6D,KAAA,EAAA,IACA7D,KAAA6D,KAAA,GAAA,IACA7D,KAAA6D,KAAA,GACA,IAAA7D,KAAA8D,GACA9D,KAAA8D,KAAA,EAAA,IACA9D,KAAA8D,KAAA,GAAA,IACA9D,KAAA8D,KAAA,EACA,CACA,EAMAsZ,EAAAld,UAAAkmB,SAAA,WACA,IAAAQ,EAAA5mB,KAAA8D,IAAA,GAGA,OAFA9D,KAAA8D,KAAA9D,KAAA8D,IAAA,EAAA9D,KAAA6D,KAAA,IAAA+iB,KAAA,EACA5mB,KAAA6D,IAAA7D,KAAA6D,IAAA,EAAA+iB,KAAA,EACA5mB,IACA,EAMAod,EAAAld,UAAA8e,SAAA,WACA,IAAA4H,EAAA,EAAA,EAAA5mB,KAAA6D,IAGA,OAFA7D,KAAA6D,KAAA7D,KAAA6D,KAAA,EAAA7D,KAAA8D,IAAA,IAAA8iB,KAAA,EACA5mB,KAAA8D,IAAA9D,KAAA8D,KAAA,EAAA8iB,KAAA,EACA5mB,IACA,EAMAod,EAAAld,UAAAtE,OAAA,WACA,IAAAirB,EAAA7mB,KAAA6D,GACAijB,GAAA9mB,KAAA6D,KAAA,GAAA7D,KAAA8D,IAAA,KAAA,EACAijB,EAAA/mB,KAAA8D,KAAA,GACA,OAAA,GAAAijB,EACA,GAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,EACA,C,+BCtMA,IAAAlsB,EAAAD,EA2OA,SAAAgkB,EAAAqH,EAAAe,EAAAlX,GACA,IAAA,IAAA/Q,EAAAD,OAAAC,KAAAioB,CAAA,EAAAnqB,EAAA,EAAAA,EAAAkC,EAAAnD,OAAA,EAAAiB,EACAopB,EAAAlnB,EAAAlC,MAAA1C,IAAA2V,IACAmW,EAAAlnB,EAAAlC,IAAAmqB,EAAAjoB,EAAAlC,KACA,OAAAopB,CACA,CAmBA,SAAAgB,EAAAxsB,GAEA,SAAAysB,EAAA7X,EAAA2D,GAEA,GAAA,EAAAhT,gBAAAknB,GACA,OAAA,IAAAA,EAAA7X,EAAA2D,CAAA,EAKAlU,OAAA2Q,eAAAzP,KAAA,UAAA,CAAAwJ,IAAA,WAAA,OAAA6F,CAAA,CAAA,CAAA,EAGArR,MAAAmpB,kBACAnpB,MAAAmpB,kBAAAnnB,KAAAknB,CAAA,EAEApoB,OAAA2Q,eAAAzP,KAAA,QAAA,CAAAP,MAAAzB,MAAA,EAAA4kB,OAAA,EAAA,CAAA,EAEA5P,GACA4L,EAAA5e,KAAAgT,CAAA,CACA,CA2BA,OAzBAkU,EAAAhnB,UAAApB,OAAAgO,OAAA9O,MAAAkC,UAAA,CACA6M,YAAA,CACAtN,MAAAynB,EACAE,SAAA,CAAA,EACApB,WAAA,CAAA,EACAqB,aAAA,CAAA,CACA,EACA5sB,KAAA,CACA+O,IAAA,WAAA,OAAA/O,CAAA,EACAsd,IAAA5d,GACA6rB,WAAA,CAAA,EAKAqB,aAAA,CAAA,CACA,EACA5oB,SAAA,CACAgB,MAAA,WAAA,OAAAO,KAAAvF,KAAA,KAAAuF,KAAAqP,OAAA,EACA+X,SAAA,CAAA,EACApB,WAAA,CAAA,EACAqB,aAAA,CAAA,CACA,CACA,CAAA,EAEAH,CACA,CAhTArsB,EAAA8F,UAAArF,EAAA,CAAA,EAGAT,EAAAwB,OAAAf,EAAA,CAAA,EAGAT,EAAAkF,aAAAzE,EAAA,CAAA,EAGAT,EAAA0jB,MAAAjjB,EAAA,CAAA,EAGAT,EAAAqK,QAAA5J,EAAA,CAAA,EAGAT,EAAAyL,KAAAhL,EAAA,EAAA,EAGAT,EAAAysB,KAAAhsB,EAAA,CAAA,EAGAT,EAAAuiB,SAAA9hB,EAAA,EAAA,EAOAT,EAAA0lB,OAAAjS,CAAAA,EAAA,aAAA,OAAAxT,QACAA,QACAA,OAAAmlB,SACAnlB,OAAAmlB,QAAAsH,UACAzsB,OAAAmlB,QAAAsH,SAAAC,MAOA3sB,EAAAC,OAAAD,EAAA0lB,QAAAzlB,QACA,aAAA,OAAA2sB,QAAAA,QACA,aAAA,OAAAhI,MAAAA,MACAzf,KAQAnF,EAAA0V,WAAAzR,OAAAsR,OAAAtR,OAAAsR,OAAA,EAAA,EAAA,GAOAvV,EAAAyV,YAAAxR,OAAAsR,OAAAtR,OAAAsR,OAAA,EAAA,EAAA,GAQAvV,EAAA6T,UAAAhP,OAAAgP,WAAA,SAAAjP,GACA,MAAA,UAAA,OAAAA,GAAAioB,SAAAjoB,CAAA,GAAAhD,KAAAkD,MAAAF,CAAA,IAAAA,CACA,EAOA5E,EAAA4T,SAAA,SAAAhP,GACA,MAAA,UAAA,OAAAA,GAAAA,aAAAjC,MACA,EAOA3C,EAAAsU,SAAA,SAAA1P,GACA,OAAAA,GAAA,UAAA,OAAAA,CACA,EAUA5E,EAAA8sB,MAQA9sB,EAAA+sB,MAAA,SAAA3T,EAAAlK,GACA,IAAAtK,EAAAwU,EAAAlK,GACA,OAAA,MAAAtK,GAAAwU,EAAA+B,eAAAjM,CAAA,IACA,UAAA,OAAAtK,GAAA,GAAA/D,MAAAyZ,QAAA1V,CAAA,EAAAA,EAAAX,OAAAC,KAAAU,CAAA,GAAA7D,OAEA,EAaAf,EAAA2iB,OAAA,WACA,IACA,IAAAA,EAAA3iB,EAAAqK,QAAA,QAAA,EAAAsY,OAEA,OAAAA,EAAAtd,UAAA2nB,UAAArK,EAAA,IAIA,CAHA,MAAAlY,GAEA,OAAA,IACA,CACA,EAAA,EAGAzK,EAAAitB,EAAA,KAGAjtB,EAAAktB,EAAA,KAOAltB,EAAAwV,UAAA,SAAA2X,GAEA,MAAA,UAAA,OAAAA,EACAntB,EAAA2iB,OACA3iB,EAAAktB,EAAAC,CAAA,EACA,IAAAntB,EAAAa,MAAAssB,CAAA,EACAntB,EAAA2iB,OACA3iB,EAAAitB,EAAAE,CAAA,EACA,aAAA,OAAAtmB,WACAsmB,EACA,IAAAtmB,WAAAsmB,CAAA,CACA,EAMAntB,EAAAa,MAAA,aAAA,OAAAgG,WAAAA,WAAAhG,MAeAb,EAAAI,KAAAJ,EAAAC,OAAAmtB,SAAAptB,EAAAC,OAAAmtB,QAAAhtB,MACAJ,EAAAC,OAAAG,MACAJ,EAAAqK,QAAA,MAAA,EAOArK,EAAAqtB,OAAA,mBAOArtB,EAAAstB,QAAA,wBAOAttB,EAAAutB,QAAA,6CAOAvtB,EAAAwtB,WAAA,SAAA5oB,GACA,OAAAA,EACA5E,EAAAuiB,SAAAkJ,KAAA7mB,CAAA,EAAAknB,OAAA,EACA9rB,EAAAuiB,SAAAiJ,QACA,EAQAxrB,EAAAytB,aAAA,SAAA5B,EAAAjb,GACAmS,EAAA/iB,EAAAuiB,SAAAqJ,SAAAC,CAAA,EACA,OAAA7rB,EAAAI,KACAJ,EAAAI,KAAAstB,SAAA3K,EAAA/Z,GAAA+Z,EAAA9Z,GAAA2H,CAAA,EACAmS,EAAAlS,SAAA4C,CAAAA,CAAA7C,CAAA,CACA,EAiBA5Q,EAAA+jB,MAAAA,EAOA/jB,EAAAqhB,QAAA,SAAAxB,GACA,OAAAA,EAAA,IAAAA,IAAAtL,YAAA,EAAAsL,EAAAP,UAAA,CAAA,CACA,EA0DAtf,EAAAosB,SAAAA,EAmBApsB,EAAA2tB,cAAAvB,EAAA,eAAA,EAoBApsB,EAAAid,YAAA,SAAAH,GAEA,IADA,IAAA8Q,EAAA,GACA5rB,EAAA,EAAAA,EAAA8a,EAAA/b,OAAA,EAAAiB,EACA4rB,EAAA9Q,EAAA9a,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAiB,IAAA,EAAAnD,EAAAkC,EAAAnD,OAAA,EAAA,CAAA,EAAAiB,EAAA,EAAAA,EACA,GAAA,IAAA4rB,EAAA1pB,EAAAlC,KAAAmD,KAAAjB,EAAAlC,MAAA1C,IAAA,OAAA6F,KAAAjB,EAAAlC,IACA,OAAAkC,EAAAlC,EACA,CACA,EAeAhC,EAAAmd,YAAA,SAAAL,GAQA,OAAA,SAAAld,GACA,IAAA,IAAAoC,EAAA,EAAAA,EAAA8a,EAAA/b,OAAA,EAAAiB,EACA8a,EAAA9a,KAAApC,GACA,OAAAuF,KAAA2X,EAAA9a,GACA,CACA,EAkBAhC,EAAAuT,cAAA,CACAsa,MAAAlrB,OACAmrB,MAAAnrB,OACAmO,MAAAnO,OACAsJ,KAAA,CAAA,CACA,EAGAjM,EAAAwW,EAAA,WACA,IAAAmM,EAAA3iB,EAAA2iB,OAEAA,GAMA3iB,EAAAitB,EAAAtK,EAAA8I,OAAA5kB,WAAA4kB,MAAA9I,EAAA8I,MAEA,SAAA7mB,EAAAmpB,GACA,OAAA,IAAApL,EAAA/d,EAAAmpB,CAAA,CACA,EACA/tB,EAAAktB,EAAAvK,EAAAqL,aAEA,SAAA3iB,GACA,OAAA,IAAAsX,EAAAtX,CAAA,CACA,GAdArL,EAAAitB,EAAAjtB,EAAAktB,EAAA,IAeA,C,6DCpbA3sB,EAAAR,QAwHA,SAAA6P,GAGA,IAAAb,EAAA/O,EAAAqD,QAAA,CAAA,KAAAuM,EAAAhQ,KAAA,SAAA,EACA,mCAAA,EACA,WAAA,iBAAA,EACAsN,EAAA0C,EAAAqa,YACAgE,EAAA,GACA/gB,EAAAnM,QAAAgO,EACA,UAAA,EAEA,IAAA,IAAA/M,EAAA,EAAAA,EAAA4N,EAAAC,YAAA9O,OAAA,EAAAiB,EAAA,CACA,IA2BAksB,EA3BAlf,EAAAY,EAAAoB,EAAAhP,GAAAZ,QAAA,EACA+P,EAAA,IAAAnR,EAAA8P,SAAAd,EAAApP,IAAA,EAEAoP,EAAA8C,UAAA/C,EACA,sCAAAoC,EAAAnC,EAAApP,IAAA,EAGAoP,EAAAe,KAAAhB,EACA,yBAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,QAAA,CAAA,EACA,wBAAAmC,CAAA,EACA,8BAAA,EAxDA,SAAApC,EAAAC,EAAAmC,GAEA,OAAAnC,EAAAhC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAA+B,EACA,6BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,aAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,kBAAA,CAAA,EACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,aAAA,CAAA,CAEA,CAGA,EA+BAD,EAAAC,EAAA,MAAA,EACAof,EAAArf,EAAAC,EAAAhN,EAAAmP,EAAA,QAAA,EACA,GAAA,GAGAnC,EAAAM,UAAAP,EACA,yBAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,OAAA,CAAA,EACA,gCAAAmC,CAAA,EACAid,EAAArf,EAAAC,EAAAhN,EAAAmP,EAAA,KAAA,EACA,GAAA,IAIAnC,EAAAsB,SACA4d,EAAAluB,EAAA8P,SAAAd,EAAAsB,OAAA1Q,IAAA,EACA,IAAAquB,EAAAjf,EAAAsB,OAAA1Q,OAAAmP,EACA,cAAAmf,CAAA,EACA,WAAAlf,EAAAsB,OAAA1Q,KAAA,mBAAA,EACAquB,EAAAjf,EAAAsB,OAAA1Q,MAAA,EACAmP,EACA,QAAAmf,CAAA,GAEAE,EAAArf,EAAAC,EAAAhN,EAAAmP,CAAA,GAEAnC,EAAA8C,UAAA/C,EACA,GAAA,CACA,CACA,OAAAA,EACA,aAAA,CAEA,EA7KA,IAAAF,EAAApO,EAAA,EAAA,EACAT,EAAAS,EAAA,EAAA,EAEA,SAAA0tB,EAAAnf,EAAA0a,GACA,OAAA1a,EAAApP,KAAA,KAAA8pB,GAAA1a,EAAAM,UAAA,UAAAoa,EAAA,KAAA1a,EAAAe,KAAA,WAAA2Z,EAAA,MAAA1a,EAAAhC,QAAA,IAAA,IAAA,WACA,CAWA,SAAAohB,EAAArf,EAAAC,EAAAC,EAAAkC,GAEA,GAAAnC,EAAAI,aACA,GAAAJ,EAAAI,wBAAAP,EAAA,CAAAE,EACA,cAAAoC,CAAA,EACA,UAAA,EACA,WAAAgd,EAAAnf,EAAA,YAAA,CAAA,EACA,IAAA,IAAA9K,EAAAD,OAAAC,KAAA8K,EAAAI,aAAAxB,MAAA,EAAApL,EAAA,EAAAA,EAAA0B,EAAAnD,OAAA,EAAAyB,EAAAuM,EACA,WAAAC,EAAAI,aAAAxB,OAAA1J,EAAA1B,GAAA,EACAuM,EACA,OAAA,EACA,GAAA,CACA,MACAA,EACA,GAAA,EACA,8BAAAE,EAAAkC,CAAA,EACA,OAAA,EACA,aAAAnC,EAAApP,KAAA,GAAA,EACA,GAAA,OAGA,OAAAoP,EAAAzC,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAwC,EACA,0BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,SAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAoC,EAAAA,EAAAA,EAAAA,CAAA,EACA,WAAAgd,EAAAnf,EAAA,cAAA,CAAA,EACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,QAAA,CAAA,EACA,MACA,IAAA,OAAAD,EACA,4BAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,SAAA,CAAA,EACA,MACA,IAAA,SAAAD,EACA,yBAAAoC,CAAA,EACA,WAAAgd,EAAAnf,EAAA,QAAA,CAAA,EACA,MACA,IAAA,QAAAD,EACA,4DAAAoC,EAAAA,EAAAA,CAAA,EACA,WAAAgd,EAAAnf,EAAA,QAAA,CAAA,CAEA,CAEA,OAAAD,CAEA,C,qCCvEA,IAEAuI,EAAA7W,EAAA,EAAA,EA6BA8W,EAAA,wBAAA,CAEA5H,WAAA,SAAA+I,GAGA,GAAAA,GAAAA,EAAA,SAAA,CAEA,IAKApM,EALA1M,EAAA8Y,EAAA,SAAA4G,UAAA,EAAA5G,EAAA,SAAAwM,YAAA,GAAA,CAAA,EACA3Y,EAAApH,KAAAwV,OAAA/a,CAAA,EAEA,GAAA2M,EAQA,MAHAD,EAHAA,EAAA,MAAAoM,EAAA,SAAA,IAAAA,IACAA,EAAA,SAAA7V,MAAA,CAAA,EAAA6V,EAAA,UAEAzH,QAAA,GAAA,IACA3E,EAAA,IAAAA,GAEAnH,KAAA8M,OAAA,CACA3F,SAAAA,EACA1H,MAAA2H,EAAAtK,OAAAsK,EAAAoD,WAAA+I,CAAA,CAAA,EAAAoM,OAAA,CACA,CAAA,CAEA,CAEA,OAAA3f,KAAAwK,WAAA+I,CAAA,CACA,EAEA1I,SAAA,SAAAwE,EAAAvO,GAGA,IAkBAyS,EACA2V,EAlBAtjB,EAAA,GACAnL,EAAA,GAeA,OAZAqG,GAAAA,EAAAgG,MAAAuI,EAAAlI,UAAAkI,EAAA5P,QAEAhF,EAAA4U,EAAAlI,SAAAgT,UAAA,EAAA9K,EAAAlI,SAAA4Y,YAAA,GAAA,CAAA,EAEAna,EAAAyJ,EAAAlI,SAAAgT,UAAA,EAAA,EAAA9K,EAAAlI,SAAA4Y,YAAA,GAAA,CAAA,GACA3Y,EAAApH,KAAAwV,OAAA/a,CAAA,KAGA4U,EAAAjI,EAAAvJ,OAAAwR,EAAA5P,KAAA,IAIA,EAAA4P,aAAArP,KAAAwQ,OAAAnB,aAAA8C,GACAoB,EAAAlE,EAAA4D,MAAApI,SAAAwE,EAAAvO,CAAA,EACAooB,EAAA,MAAA7Z,EAAA4D,MAAA7I,SAAA,GACAiF,EAAA4D,MAAA7I,SAAA1M,MAAA,CAAA,EAAA2R,EAAA4D,MAAA7I,SAMAmJ,EAAA,SADA9Y,GAFAmL,EADA,KAAAA,EAtBA,uBAyBAA,GAAAsjB,EAEA3V,GAGAvT,KAAA6K,SAAAwE,EAAAvO,CAAA,CACA,CACA,C,+BCpGA1F,EAAAR,QAAAyX,EAEA,IAEAC,EAFAzX,EAAAS,EAAA,EAAA,EAIA8hB,EAAAviB,EAAAuiB,SACA/gB,EAAAxB,EAAAwB,OACAiK,EAAAzL,EAAAyL,KAWA,SAAA6iB,EAAA5tB,EAAAgL,EAAArE,GAMAlC,KAAAzE,GAAAA,EAMAyE,KAAAuG,IAAAA,EAMAvG,KAAAmZ,KAAAhf,GAMA6F,KAAAkC,IAAAA,CACA,CAGA,SAAAknB,KAUA,SAAAC,EAAAnW,GAMAlT,KAAAuZ,KAAArG,EAAAqG,KAMAvZ,KAAAspB,KAAApW,EAAAoW,KAMAtpB,KAAAuG,IAAA2M,EAAA3M,IAMAvG,KAAAmZ,KAAAjG,EAAAqW,MACA,CAOA,SAAAlX,IAMArS,KAAAuG,IAAA,EAMAvG,KAAAuZ,KAAA,IAAA4P,EAAAC,EAAA,EAAA,CAAA,EAMAppB,KAAAspB,KAAAtpB,KAAAuZ,KAMAvZ,KAAAupB,OAAA,IAOA,CAEA,SAAAzc,IACA,OAAAjS,EAAA2iB,OACA,WACA,OAAAnL,EAAAvF,OAAA,WACA,OAAA,IAAAwF,CACA,GAAA,CACA,EAEA,WACA,OAAA,IAAAD,CACA,CACA,CAqCA,SAAAmX,EAAAtnB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,CACA,CAmBA,SAAAunB,EAAAljB,EAAArE,GACAlC,KAAAuG,IAAAA,EACAvG,KAAAmZ,KAAAhf,GACA6F,KAAAkC,IAAAA,CACA,CA6CA,SAAAwnB,EAAAxnB,EAAAC,EAAAC,GACA,KAAAF,EAAA4B,IACA3B,EAAAC,CAAA,IAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,IAAA3B,EAAA2B,KAAA,EAAA3B,EAAA4B,IAAA,MAAA,EACA5B,EAAA4B,MAAA,EAEA,KAAA,IAAA5B,EAAA2B,IACA1B,EAAAC,CAAA,IAAA,IAAAF,EAAA2B,GAAA,IACA3B,EAAA2B,GAAA3B,EAAA2B,KAAA,EAEA1B,EAAAC,CAAA,IAAAF,EAAA2B,EACA,CA0CA,SAAA8lB,EAAAznB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EACA,CA9JAmQ,EAAAvF,OAAAA,EAAA,EAOAuF,EAAApM,MAAA,SAAAC,GACA,OAAA,IAAArL,EAAAa,MAAAwK,CAAA,CACA,EAIArL,EAAAa,QAAAA,QACA2W,EAAApM,MAAApL,EAAAysB,KAAAjV,EAAApM,MAAApL,EAAAa,MAAAwE,UAAA8d,QAAA,GAUA3L,EAAAnS,UAAA0pB,EAAA,SAAAruB,EAAAgL,EAAArE,GAGA,OAFAlC,KAAAspB,KAAAtpB,KAAAspB,KAAAnQ,KAAA,IAAAgQ,EAAA5tB,EAAAgL,EAAArE,CAAA,EACAlC,KAAAuG,KAAAA,EACAvG,IACA,GA6BAypB,EAAAvpB,UAAApB,OAAAgO,OAAAqc,EAAAjpB,SAAA,GACA3E,GAxBA,SAAA2G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,CAAA,IAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,CACA,EAyBAmQ,EAAAnS,UAAA+d,OAAA,SAAAxe,GAWA,OARAO,KAAAuG,MAAAvG,KAAAspB,KAAAtpB,KAAAspB,KAAAnQ,KAAA,IAAAsQ,GACAhqB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,CAAA,GAAA8G,IACAvG,IACA,EAQAqS,EAAAnS,UAAAge,MAAA,SAAAze,GACA,OAAAA,EAAA,EACAO,KAAA4pB,EAAAF,EAAA,GAAAtM,EAAAjN,WAAA1Q,CAAA,CAAA,EACAO,KAAAie,OAAAxe,CAAA,CACA,EAOA4S,EAAAnS,UAAAie,OAAA,SAAA1e,GACA,OAAAO,KAAAie,QAAAxe,GAAA,EAAAA,GAAA,MAAA,CAAA,CACA,EAiCA4S,EAAAnS,UAAA2e,MAZAxM,EAAAnS,UAAA4e,OAAA,SAAArf,GACAme,EAAAR,EAAAkJ,KAAA7mB,CAAA,EACA,OAAAO,KAAA4pB,EAAAF,EAAA9L,EAAAhiB,OAAA,EAAAgiB,CAAA,CACA,EAiBAvL,EAAAnS,UAAA6e,OAAA,SAAAtf,GACAme,EAAAR,EAAAkJ,KAAA7mB,CAAA,EAAA2mB,SAAA,EACA,OAAApmB,KAAA4pB,EAAAF,EAAA9L,EAAAhiB,OAAA,EAAAgiB,CAAA,CACA,EAOAvL,EAAAnS,UAAAke,KAAA,SAAA3e,GACA,OAAAO,KAAA4pB,EAAAJ,EAAA,EAAA/pB,EAAA,EAAA,CAAA,CACA,EAwBA4S,EAAAnS,UAAAoe,SAVAjM,EAAAnS,UAAAme,QAAA,SAAA5e,GACA,OAAAO,KAAA4pB,EAAAD,EAAA,EAAAlqB,IAAA,CAAA,CACA,EA4BA4S,EAAAnS,UAAAgf,SAZA7M,EAAAnS,UAAA+e,QAAA,SAAAxf,GACAme,EAAAR,EAAAkJ,KAAA7mB,CAAA,EACA,OAAAO,KAAA4pB,EAAAD,EAAA,EAAA/L,EAAA/Z,EAAA,EAAA+lB,EAAAD,EAAA,EAAA/L,EAAA9Z,EAAA,CACA,EAiBAuO,EAAAnS,UAAAqe,MAAA,SAAA9e,GACA,OAAAO,KAAA4pB,EAAA/uB,EAAA0jB,MAAAna,aAAA,EAAA3E,CAAA,CACA,EAQA4S,EAAAnS,UAAAse,OAAA,SAAA/e,GACA,OAAAO,KAAA4pB,EAAA/uB,EAAA0jB,MAAAzZ,cAAA,EAAArF,CAAA,CACA,EAEA,IAAAoqB,EAAAhvB,EAAAa,MAAAwE,UAAA6X,IACA,SAAA7V,EAAAC,EAAAC,GACAD,EAAA4V,IAAA7V,EAAAE,CAAA,CACA,EAEA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAAvF,EAAA,EAAAA,EAAAqF,EAAAtG,OAAA,EAAAiB,EACAsF,EAAAC,EAAAvF,GAAAqF,EAAArF,EACA,EAOAwV,EAAAnS,UAAAyL,MAAA,SAAAlM,GACA,IAIA0C,EAJAoE,EAAA9G,EAAA7D,SAAA,EACA,OAAA2K,GAEA1L,EAAA4T,SAAAhP,CAAA,IACA0C,EAAAkQ,EAAApM,MAAAM,EAAAlK,EAAAT,OAAA6D,CAAA,CAAA,EACApD,EAAAwB,OAAA4B,EAAA0C,EAAA,CAAA,EACA1C,EAAA0C,GAEAnC,KAAAie,OAAA1X,CAAA,EAAAqjB,EAAAC,EAAAtjB,EAAA9G,CAAA,GANAO,KAAA4pB,EAAAJ,EAAA,EAAA,CAAA,CAOA,EAOAnX,EAAAnS,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAAD,EAAA1K,OAAA6D,CAAA,EACA,OAAA8G,EACAvG,KAAAie,OAAA1X,CAAA,EAAAqjB,EAAAtjB,EAAAG,MAAAF,EAAA9G,CAAA,EACAO,KAAA4pB,EAAAJ,EAAA,EAAA,CAAA,CACA,EAOAnX,EAAAnS,UAAAklB,KAAA,WAIA,OAHAplB,KAAAupB,OAAA,IAAAF,EAAArpB,IAAA,EACAA,KAAAuZ,KAAAvZ,KAAAspB,KAAA,IAAAH,EAAAC,EAAA,EAAA,CAAA,EACAppB,KAAAuG,IAAA,EACAvG,IACA,EAMAqS,EAAAnS,UAAA4pB,MAAA,WAUA,OATA9pB,KAAAupB,QACAvpB,KAAAuZ,KAAAvZ,KAAAupB,OAAAhQ,KACAvZ,KAAAspB,KAAAtpB,KAAAupB,OAAAD,KACAtpB,KAAAuG,IAAAvG,KAAAupB,OAAAhjB,IACAvG,KAAAupB,OAAAvpB,KAAAupB,OAAApQ,OAEAnZ,KAAAuZ,KAAAvZ,KAAAspB,KAAA,IAAAH,EAAAC,EAAA,EAAA,CAAA,EACAppB,KAAAuG,IAAA,GAEAvG,IACA,EAMAqS,EAAAnS,UAAAmlB,OAAA,WACA,IAAA9L,EAAAvZ,KAAAuZ,KACA+P,EAAAtpB,KAAAspB,KACA/iB,EAAAvG,KAAAuG,IAOA,OANAvG,KAAA8pB,MAAA,EAAA7L,OAAA1X,CAAA,EACAA,IACAvG,KAAAspB,KAAAnQ,KAAAI,EAAAJ,KACAnZ,KAAAspB,KAAAA,EACAtpB,KAAAuG,KAAAA,GAEAvG,IACA,EAMAqS,EAAAnS,UAAAyf,OAAA,WAIA,IAHA,IAAApG,EAAAvZ,KAAAuZ,KAAAJ,KACAhX,EAAAnC,KAAA+M,YAAA9G,MAAAjG,KAAAuG,GAAA,EACAnE,EAAA,EACAmX,GACAA,EAAAhe,GAAAge,EAAArX,IAAAC,EAAAC,CAAA,EACAA,GAAAmX,EAAAhT,IACAgT,EAAAA,EAAAJ,KAGA,OAAAhX,CACA,EAEAkQ,EAAAhB,EAAA,SAAA0Y,GACAzX,EAAAyX,EACA1X,EAAAvF,OAAAA,EAAA,EACAwF,EAAAjB,EAAA,CACA,C,+BC/cAjW,EAAAR,QAAA0X,EAGA,IAAAD,EAAA/W,EAAA,EAAA,EAGAT,IAFAyX,EAAApS,UAAApB,OAAAgO,OAAAuF,EAAAnS,SAAA,GAAA6M,YAAAuF,EAEAhX,EAAA,EAAA,GAQA,SAAAgX,IACAD,EAAA1X,KAAAqF,IAAA,CACA,CAuCA,SAAAgqB,EAAA9nB,EAAAC,EAAAC,GACAF,EAAAtG,OAAA,GACAf,EAAAyL,KAAAG,MAAAvE,EAAAC,EAAAC,CAAA,EACAD,EAAA0lB,UACA1lB,EAAA0lB,UAAA3lB,EAAAE,CAAA,EAEAD,EAAAsE,MAAAvE,EAAAE,CAAA,CACA,CA5CAkQ,EAAAjB,EAAA,WAOAiB,EAAArM,MAAApL,EAAAktB,EAEAzV,EAAA2X,iBAAApvB,EAAA2iB,QAAA3iB,EAAA2iB,OAAAtd,qBAAAwB,YAAA,QAAA7G,EAAA2iB,OAAAtd,UAAA6X,IAAAtd,KACA,SAAAyH,EAAAC,EAAAC,GACAD,EAAA4V,IAAA7V,EAAAE,CAAA,CAEA,EAEA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAAgoB,KACAhoB,EAAAgoB,KAAA/nB,EAAAC,EAAA,EAAAF,EAAAtG,MAAA,OACA,IAAA,IAAAiB,EAAA,EAAAA,EAAAqF,EAAAtG,QACAuG,EAAAC,CAAA,IAAAF,EAAArF,CAAA,GACA,CACA,EAMAyV,EAAApS,UAAAyL,MAAA,SAAAlM,GAGA,IAAA8G,GADA9G,EADA5E,EAAA4T,SAAAhP,CAAA,EACA5E,EAAAitB,EAAAroB,EAAA,QAAA,EACAA,GAAA7D,SAAA,EAIA,OAHAoE,KAAAie,OAAA1X,CAAA,EACAA,GACAvG,KAAA4pB,EAAAtX,EAAA2X,iBAAA1jB,EAAA9G,CAAA,EACAO,IACA,EAcAsS,EAAApS,UAAA5D,OAAA,SAAAmD,GACA,IAAA8G,EAAA1L,EAAA2iB,OAAA2M,WAAA1qB,CAAA,EAIA,OAHAO,KAAAie,OAAA1X,CAAA,EACAA,GACAvG,KAAA4pB,EAAAI,EAAAzjB,EAAA9G,CAAA,EACAO,IACA,EAUAsS,EAAAjB,EAAA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number|Long} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number|Long} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop) {\n    var defaultAlreadyEmitted = false;\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(d%s){\", prop);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                // enum unknown values passthrough\n                if (values[keys[i]] === field.typeDefault && !defaultAlreadyEmitted) { gen\n                    (\"default:\")\n                        (\"if(typeof(d%s)===\\\"number\\\"){m%s=d%s;break}\", prop, prop, prop);\n                    if (!field.repeated) gen // fallback to default value only for\n                                             // arrays, to avoid leaving holes.\n                        (\"break\");           // for non-repeated fields, just ignore\n                    defaultAlreadyEmitted = true;\n                }\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof d%s!==\\\"object\\\")\", prop)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(d%s)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(d%s)\", prop, prop); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=d%s>>>0\", prop, prop);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=d%s|0\", prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(d%s)).unsigned=%j\", prop, prop, isUnsigned)\n                (\"else if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"m%s=parseInt(d%s,10)\", prop, prop)\n                (\"else if(typeof d%s===\\\"number\\\")\", prop)\n                    (\"m%s=d%s\", prop, prop)\n                (\"else if(typeof d%s===\\\"object\\\")\", prop)\n                    (\"m%s=new util.LongBits(d%s.low>>>0,d%s.high>>>0).toNumber(%s)\", prop, prop, prop, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof d%s===\\\"string\\\")\", prop)\n                    (\"util.base64.decode(d%s,m%s=util.newBuffer(util.base64.length(d%s)),0)\", prop, prop, prop)\n                (\"else if(d%s.length >= 0)\", prop)\n                    (\"m%s=d%s\", prop, prop);\n                break;\n            case \"string\": gen\n                (\"m%s=String(d%s)\", prop, prop);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(d%s)\", prop, prop);\n                break;\n            /* default: gen\n                (\"m%s=d%s\", prop, prop);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(d%s){\", prop)\n        (\"if(!Array.isArray(d%s))\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<d%s.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?(types[%i].values[m%s]===undefined?m%s:types[%i].values[m%s]):m%s\", prop, fieldIndex, prop, prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-next-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\", \"e\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k,value\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\")\n        (\"if(t===e)\")\n            (\"break\")\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i: {\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"var c2 = r.uint32()+r.pos\");\n\n            if (types.defaults[field.keyType] !== undefined) gen\n                (\"k=%j\", types.defaults[field.keyType]);\n            else gen\n                (\"k=null\");\n\n            if (types.defaults[type] !== undefined) gen\n                (\"value=%j\", types.defaults[type]);\n            else gen\n                (\"value=null\");\n\n            gen\n                (\"while(r.pos<c2){\")\n                    (\"var tag2=r.uint32()\")\n                    (\"switch(tag2>>>3){\")\n                        (\"case 1: k=r.%s(); break\", field.keyType)\n                        (\"case 2:\");\n\n            if (types.basic[type] === undefined) gen\n                            (\"value=types[%i].decode(r,r.uint32())\", i); // can't be groups\n            else gen\n                            (\"value=r.%s()\", type);\n\n            gen\n                            (\"break\")\n                        (\"default:\")\n                            (\"r.skipType(tag2&7)\")\n                            (\"break\")\n                    (\"}\")\n                (\"}\");\n\n            if (types.long[field.keyType] !== undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=value\", ref);\n            else gen\n                (\"%s[k]=value\", ref);\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.delimited\n                    ? \"%s.push(types[%i].decode(r,undefined,((t&~7)|4)))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.delimited\n                ? \"%s=types[%i].decode(r,undefined,((t&~7)|4))\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\")\n            (\"}\");\n        // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n    return field.delimited\n        ? gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0)\n        : gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, (field.id << 3 | 2) >>> 0);\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) { gen\n    (\"if(%s!=null&&%s.length){\", ref, ref); // !== undefined && !== null\n\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", ref)\n            (\"w.%s(%s[i])\", type, ref)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", ref);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, ref + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n * @param {Object.<string,Object<string,*>>|undefined} [valuesOptions] The value options for this enum\n */\nfunction Enum(name, values, options, comment, comments, valuesOptions) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Values options, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this.valuesOptions = valuesOptions;\n\n    /**\n     * Resolved values features, if any\n     * @type {Object<string, Object<string, *>>|undefined}\n     */\n    this._valuesFeatures = {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * @override\n */\nEnum.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    edition = this._edition || edition;\n    ReflectionObject.prototype._resolveFeatures.call(this, edition);\n\n    Object.keys(this.values).forEach(key => {\n        var parentFeaturesCopy = Object.assign({}, this._features);\n        this._valuesFeatures[key] = Object.assign(parentFeaturesCopy, this.valuesOptions && this.valuesOptions[key] && this.valuesOptions[key].features);\n    });\n\n    return this;\n};\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    if (json.edition)\n        enm._edition = json.edition;\n    enm._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\"       , this._editionToJSON(),\n        \"options\"       , this.options,\n        \"valuesOptions\" , this.valuesOptions,\n        \"values\"        , this.values,\n        \"reserved\"      , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"       , keepComments ? this.comment : undefined,\n        \"comments\"      , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @param {Object.<string, *>|undefined} [options] Options, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment, options) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    if (options) {\n        if (this.valuesOptions === undefined)\n            this.valuesOptions = {};\n        this.valuesOptions[name] = options || null;\n    }\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n    if (this.valuesOptions)\n        delete this.valuesOptions[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    var field = new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n    if (json.edition)\n        field._edition = json.edition;\n    field._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return field;\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    if (rule === \"proto3_optional\") {\n        rule = \"optional\";\n    }\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is required.\n * @name Field#required\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"required\", {\n    get: function() {\n        return this._features.field_presence === \"LEGACY_REQUIRED\";\n    }\n});\n\n/**\n * Determines whether this field is not required.\n * @name Field#optional\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"optional\", {\n    get: function() {\n        return !this.required;\n    }\n});\n\n/**\n * Determines whether this field uses tag-delimited encoding.  In proto2 this\n * corresponded to group syntax.\n * @name Field#delimited\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"delimited\", {\n    get: function() {\n        return this.resolvedType instanceof Type &&\n            this._features.message_encoding === \"DELIMITED\";\n    }\n});\n\n/**\n * Determines whether this field is packed. Only relevant when repeated.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        return this._features.repeated_field_encoding === \"PACKED\";\n    }\n});\n\n/**\n * Determines whether this field tracks presence.\n * @name Field#hasPresence\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"hasPresence\", {\n    get: function() {\n        if (this.repeated || this.map) {\n            return false;\n        }\n        return this.partOf || // oneofs\n            this.declaringField || this.extensionField || // extensions\n            this._features.field_presence !== \"IMPLICIT\";\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\" , this._editionToJSON(),\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    } else if (this.options && this.options.proto3_optional) {\n        // proto3 scalar value marked optional; should default to null\n        this.typeDefault = null;\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\n/**\n * Infers field features from legacy syntax that may have been specified differently.\n * in older editions.\n * @param {string|undefined} edition The edition this proto is on, or undefined if pre-editions\n * @returns {object} The feature values to override\n */\nField.prototype._inferLegacyProtoFeatures = function _inferLegacyProtoFeatures(edition) {\n    if (edition !== \"proto2\" && edition !== \"proto3\") {\n        return {};\n    }\n\n    var features = {};\n\n    if (this.rule === \"required\") {\n        features.field_presence = \"LEGACY_REQUIRED\";\n    }\n    if (this.parent && types.defaults[this.type] === undefined) {\n        // We can't use resolvedType because types may not have been resolved yet.  However,\n        // legacy groups are always in the same scope as the field so we don't have to do a\n        // full scan of the tree.\n        var type = this.parent.get(this.type.split(\".\").pop());\n        if (type && type instanceof Type && type.group) {\n            features.message_encoding = \"DELIMITED\";\n        }\n    }\n    if (this.getOption(\"packed\") === true) {\n        features.repeated_field_encoding = \"PACKED\";\n    } else if (this.getOption(\"packed\") === false) {\n        features.repeated_field_encoding = \"EXPANDED\";\n    }\n    return features;\n};\n\n/**\n * @override\n */\nField.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    return ReflectionObject.prototype._resolveFeatures.call(this, this._edition || edition);\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | Long | Long[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.util._configure();\n    protobuf.Writer._configure(protobuf.BufferWriter);\n    protobuf.Reader._configure(protobuf.BufferReader);\n}\n\n// Set up buffer utility according to the environment\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | Long | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n * @param {Object.<string,*>} [parsedOptions] Declared options, properly parsed into an object\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment, parsedOptions) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Options properly parsed into an object\n     */\n    this.parsedOptions = parsedOptions;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n * @property {string} comment Method comments\n * @property {Object.<string,*>} [parsedOptions] Method options properly parsed into an object\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment, json.parsedOptions);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined,\n        \"parsedOptions\"  , this.parsedOptions,\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    util     = require(37),\n    OneOf    = require(25);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n\n    /**\n     * Cache lookup calls for any objects contains anywhere under this namespace.\n     * This drastically speeds up resolve for large cross-linked protos where the same\n     * types are looked up repeatedly.\n     * @type {Object.<string,ReflectionObject|null>}\n     * @private\n     */\n    this._lookupCache = {};\n\n    /**\n     * Whether or not objects contained in this namespace need feature resolution.\n     * @type {boolean}\n     * @protected\n     */\n    this._needsRecursiveFeatureResolution = true;\n\n    /**\n     * Whether or not objects contained in this namespace need a resolve.\n     * @type {boolean}\n     * @protected\n     */\n    this._needsRecursiveResolve = true;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    namespace._lookupCache = {};\n\n    // Also clear parent caches, since they include nested lookups.\n    var parent = namespace;\n    while(parent = parent.parent) {\n        parent._lookupCache = {};\n    }\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace|IOneOf}\n */\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type  || object instanceof OneOf || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n\n    if (!(this instanceof Type || this instanceof Service || this instanceof Enum || this instanceof Field)) {\n        // This is a package or a root namespace.\n        if (!object._edition) {\n            // Make sure that some edition is set if it hasn't already been specified.\n            object._edition = object._defaultEdition;\n        }\n    }\n\n    this._needsRecursiveFeatureResolution = true;\n    this._needsRecursiveResolve = true;\n\n    // Also clear parent caches, since they need to recurse down.\n    var parent = this;\n    while(parent = parent.parent) {\n        parent._needsRecursiveFeatureResolution = true;\n        parent._needsRecursiveResolve = true;\n    }\n\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    this._resolveFeaturesRecursive(this._edition);\n\n    var nested = this.nestedArray, i = 0;\n    this.resolve();\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    this._needsRecursiveResolve = false;\n    return this;\n};\n\n/**\n * @override\n */\nNamespace.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n    this._needsRecursiveFeatureResolution = false;\n\n    edition = this._edition || edition;\n\n    ReflectionObject.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.nestedArray.forEach(nested => {\n        nested._resolveFeaturesRecursive(edition);\n    });\n    return this;\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    var flatPath = path.join(\".\");\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Early bailout for objects with matching absolute paths\n    var found = this.root._fullyQualifiedObjects && this.root._fullyQualifiedObjects[\".\" + flatPath];\n    if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n        return found;\n    }\n\n    // Do a regular lookup at this namespace and below\n    found = this._lookupImpl(path, flatPath);\n    if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n        return found;\n    }\n\n    if (parentAlreadyChecked)\n        return null;\n\n    // If there hasn't been a match, walk up the tree and look more broadly\n    var current = this;\n    while (current.parent) {\n        found = current.parent._lookupImpl(path, flatPath);\n        if (found && (!filterTypes || filterTypes.indexOf(found.constructor) > -1)) {\n            return found;\n        }\n        current = current.parent;\n    }\n    return null;\n};\n\n/**\n * Internal helper for lookup that handles searching just at this namespace and below along with caching.\n * @param {string[]} path Path to look up\n * @param {string} flatPath Flattened version of the path to use as a cache key\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @private\n */\nNamespace.prototype._lookupImpl = function lookup(path, flatPath) {\n    if(Object.prototype.hasOwnProperty.call(this._lookupCache, flatPath)) {\n        return this._lookupCache[flatPath];\n    }\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    var exact = null;\n    if (found) {\n        if (path.length === 1) {\n            exact = found;\n        } else if (found instanceof Namespace) {\n            path = path.slice(1);\n            exact = found._lookupImpl(path, path.join(\".\"));\n        }\n\n    // Otherwise try each nested namespace\n    } else {\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i]._lookupImpl(path, flatPath)))\n                exact = found;\n    }\n\n    // Set this even when null, so that when we walk up the tree we can quickly bail on repeated checks back down.\n    this._lookupCache[flatPath] = exact;\n    return exact;\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nconst OneOf = require(25);\nvar util = require(37);\n\nvar Root; // cyclic\n\n/* eslint-disable no-warning-comments */\n// TODO: Replace with embedded proto.\nvar editions2023Defaults = {enum_type: \"OPEN\", field_presence: \"EXPLICIT\", json_format: \"ALLOW\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"PACKED\", utf8_validation: \"VERIFY\"};\nvar proto2Defaults = {enum_type: \"CLOSED\", field_presence: \"EXPLICIT\", json_format: \"LEGACY_BEST_EFFORT\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"EXPANDED\", utf8_validation: \"NONE\"};\nvar proto3Defaults = {enum_type: \"OPEN\", field_presence: \"IMPLICIT\", json_format: \"ALLOW\", message_encoding: \"LENGTH_PREFIXED\", repeated_field_encoding: \"PACKED\", utf8_validation: \"VERIFY\"};\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Parsed Options.\n     * @type {Array.<Object.<string,*>>|undefined}\n     */\n    this.parsedOptions = null;\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * The edition specified for this object.  Only relevant for top-level objects.\n     * @type {string}\n     * @private\n     */\n    this._edition = null;\n\n    /**\n     * The default edition to use for this object if none is specified.  For legacy reasons,\n     * this is proto2 except in the JSON parsing case where it was proto3.\n     * @type {string}\n     * @private\n     */\n    this._defaultEdition = \"proto2\";\n\n    /**\n     * Resolved Features.\n     * @type {object}\n     * @private\n     */\n    this._features = {};\n\n    /**\n     * Whether or not features have been resolved.\n     * @type {boolean}\n     * @private\n     */\n    this._featuresResolved = false;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Resolves this objects editions features.\n * @param {string} edition The edition we're currently resolving for.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    return this._resolveFeatures(this._edition || edition);\n};\n\n/**\n * Resolves child features from parent features\n * @param {string} edition The edition we're currently resolving for.\n * @returns {undefined}\n */\nReflectionObject.prototype._resolveFeatures = function _resolveFeatures(edition) {\n    if (this._featuresResolved) {\n        return;\n    }\n\n    var defaults = {};\n\n    /* istanbul ignore if */\n    if (!edition) {\n        throw new Error(\"Unknown edition for \" + this.fullName);\n    }\n\n    var protoFeatures = Object.assign(this.options ? Object.assign({},  this.options.features) : {},\n        this._inferLegacyProtoFeatures(edition));\n\n    if (this._edition) {\n        // For a namespace marked with a specific edition, reset defaults.\n        /* istanbul ignore else */\n        if (edition === \"proto2\") {\n            defaults = Object.assign({}, proto2Defaults);\n        } else if (edition === \"proto3\") {\n            defaults = Object.assign({}, proto3Defaults);\n        } else if (edition === \"2023\") {\n            defaults = Object.assign({}, editions2023Defaults);\n        } else {\n            throw new Error(\"Unknown edition: \" + edition);\n        }\n        this._features = Object.assign(defaults, protoFeatures || {});\n        this._featuresResolved = true;\n        return;\n    }\n\n    // fields in Oneofs aren't actually children of them, so we have to\n    // special-case it\n    /* istanbul ignore else */\n    if (this.partOf instanceof OneOf) {\n        var lexicalParentFeaturesCopy = Object.assign({}, this.partOf._features);\n        this._features = Object.assign(lexicalParentFeaturesCopy, protoFeatures || {});\n    } else if (this.declaringField) {\n        // Skip feature resolution of sister fields.\n    } else if (this.parent) {\n        var parentFeaturesCopy = Object.assign({}, this.parent._features);\n        this._features = Object.assign(parentFeaturesCopy, protoFeatures || {});\n    } else {\n        throw new Error(\"Unable to find a parent for \" + this.fullName);\n    }\n    if (this.extensionField) {\n        // Sister fields should have the same features as their extensions.\n        this.extensionField._features = this._features;\n    }\n    this._featuresResolved = true;\n};\n\n/**\n * Infers features from legacy syntax that may have been specified differently.\n * in older editions.\n * @param {string|undefined} edition The edition this proto is on, or undefined if pre-editions\n * @returns {object} The feature values to override\n */\nReflectionObject.prototype._inferLegacyProtoFeatures = function _inferLegacyProtoFeatures(/*edition*/) {\n    return {};\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean|undefined} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!this.options)\n        this.options = {};\n    if (/^features\\./.test(name)) {\n        util.setProperty(this.options, name, value, ifNotSet);\n    } else if (!ifNotSet || this.options[name] === undefined) {\n        if (this.getOption(name) !== value) this.resolved = false;\n        this.options[name] = value;\n    }\n\n    return this;\n};\n\n/**\n * Sets a parsed option.\n * @param {string} name parsed Option name\n * @param {*} value Option value\n * @param {string} propName dot '.' delimited full path of property within the option to set. if undefined\\empty, will add a new option with that value\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setParsedOption = function setParsedOption(name, value, propName) {\n    if (!this.parsedOptions) {\n        this.parsedOptions = [];\n    }\n    var parsedOptions = this.parsedOptions;\n    if (propName) {\n        // If setting a sub property of an option then try to merge it\n        // with an existing option\n        var opt = parsedOptions.find(function (opt) {\n            return Object.prototype.hasOwnProperty.call(opt, name);\n        });\n        if (opt) {\n            // If we found an existing option - just merge the property value\n            // (If it's a feature, will just write over)\n            var newValue = opt[name];\n            util.setProperty(newValue, propName, value);\n        } else {\n            // otherwise, create a new option, set its property and add it to the list\n            opt = {};\n            opt[name] = util.setProperty({}, propName, value);\n            parsedOptions.push(opt);\n        }\n    } else {\n        // Always create a new option when setting the value of the option itself\n        var newOpt = {};\n        newOpt[name] = value;\n        parsedOptions.push(newOpt);\n    }\n\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n/**\n * Converts the edition this object is pinned to for JSON format.\n * @returns {string|undefined} The edition string for JSON representation\n */\nReflectionObject.prototype._editionToJSON = function _editionToJSON() {\n    if (!this._edition || this._edition === \"proto3\") {\n        // Avoid emitting proto3 since we need to default to it for backwards\n        // compatibility anyway.\n        return undefined;\n    }\n    return this._edition;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Determines whether this field corresponds to a synthetic oneof created for\n * a proto3 optional field.  No behavioral logic should depend on this, but it\n * can be relevant for reflection.\n * @name OneOf#isProto3Optional\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(OneOf.prototype, \"isProto3Optional\", {\n    get: function() {\n        if (this.fieldsArray == null || this.fieldsArray.length !== 1) {\n            return false;\n        }\n\n        var field = this.fieldsArray[0];\n        return field.options != null && field.options[\"proto3_optional\"] === true;\n    }\n});\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    ReflectionObject = require(24),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n * @property {boolean} [preferTrailingComment=false] Use trailing comment when both leading comment and trailing comment exist.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var preferTrailingComment = options.preferTrailingComment || false;\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        edition = \"proto2\";\n\n    var ptr = root;\n\n    var topLevelObjects = [];\n    var topLevelOptions = {};\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    function resolveFileFeatures() {\n        topLevelObjects.forEach(obj => {\n            obj._edition = edition;\n            Object.keys(topLevelOptions).forEach(opt => {\n                if (obj.getOption(opt) !== undefined) return;\n                obj.setOption(opt, topLevelOptions[opt], true);\n            });\n        });\n    }\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\")) {\n                var str = readString();\n                target.push(str);\n                if (edition >= 2023) {\n                    throw illegal(str, \"id\");\n                }\n            } else {\n                try {\n                    target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n                } catch (err) {\n                    if (acceptStrings && typeRefRe.test(token) && edition >= 2023) {\n                        target.push(token);\n                    } else {\n                        throw err;\n                    }\n                }\n            }\n        } while (skip(\",\", true));\n        var dummy = {options: undefined};\n        dummy.setOption = function(name, value) {\n          if (this.options === undefined) this.options = {};\n          this.options[name] = value;\n        };\n        ifBlock(\n            dummy,\n            function parseRange_block(token) {\n              /* istanbul ignore else */\n              if (token === \"option\") {\n                parseOption(dummy, token);  // skip\n                skip(\";\");\n              } else\n                throw illegal(token);\n            },\n            function parseRange_line() {\n              parseInlineOptions(dummy);  // skip\n            });\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-next-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        edition = readString();\n\n        /* istanbul ignore if */\n        if (edition < 2023)\n            throw illegal(edition, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseEdition() {\n        skip(\"=\");\n        edition = readString();\n        const supportedEditions = [\"2023\"];\n\n        /* istanbul ignore if */\n        if (!supportedEditions.includes(edition))\n            throw illegal(edition, \"edition\");\n\n        skip(\";\");\n    }\n\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && (typeof obj.comment !== \"string\" || preferTrailingComment))\n                obj.comment = cmnt(trailingLine) || obj.comment; // try line-type comment\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                    if (edition !== \"proto2\")\n                        throw illegal(token);\n                /* eslint-disable no-fallthrough */\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(type, \"proto3_optional\");\n                    } else if (edition !== \"proto2\") {\n                        throw illegal(token);\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (edition === \"proto2\" || !typeRefRe.test(token)) {\n                        throw illegal(token);\n                    }\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n        if (parent === ptr) {\n            topLevelObjects.push(type);\n        }\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n        // Type names can consume multiple tokens, in multiple variants:\n        //    package.subpackage   field       tokens: \"package.subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package . subpackage field       tokens: \"package\" \".\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package.  subpackage field       tokens: \"package.\" \"subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        //    package  .subpackage field       tokens: \"package\" \".subpackage\" [TYPE NAME ENDS HERE] \"field\"\n        // Keep reading tokens until we get a type name with no period at the end,\n        // and the next token does not start with a period.\n        while (type.endsWith(\".\") || peek().startsWith(\".\")) {\n            type += next();\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n\n        if (rule === \"proto3_optional\") {\n            // for proto3 optional fields, we create a single-member Oneof to mimic \"optional\" behavior\n            var oneof = new OneOf(\"_\" + name);\n            field.setOption(\"proto3_optional\", true);\n            oneof.add(field);\n            parent.add(oneof);\n        } else {\n            parent.add(field);\n        }\n        if (parent === ptr) {\n            topLevelObjects.push(field);\n        }\n    }\n\n    function parseGroup(parent, rule) {\n        if (edition >= 2023) {\n            throw illegal(\"group\");\n        }\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n                case \"required\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(type, \"proto3_optional\");\n                    } else {\n                        parseField(type, \"optional\");\n                    }\n                    break;\n\n                case \"message\":\n                    parseType(type, token);\n                    break;\n\n                case \"enum\":\n                    parseEnum(type, token);\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              if(enm.reserved === undefined) enm.reserved = [];\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n        if (parent === ptr) {\n            topLevelObjects.push(enm);\n        }\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {\n                options: undefined\n            };\n        dummy.getOption = function(name) {\n            return this.options[name];\n        };\n        dummy.setOption = function(name, value) {\n            ReflectionObject.prototype.setOption.call(dummy, name, value);\n        };\n        dummy.setParsedOption = function() {\n            return undefined;\n        };\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment, dummy.parsedOptions || dummy.options);\n    }\n\n    function parseOption(parent, token) {\n            var option;\n            var propName;\n            var isOption = true;\n            if (token === \"option\") {\n                token = next();\n            }\n\n            while (token !== \"=\") {\n                if (token === \"(\") {\n                    var parensValue = next();\n                    skip(\")\");\n                    token = \"(\" + parensValue + \")\";\n                }\n                if (isOption) {\n                    isOption = false;\n                    if (token.includes(\".\") && !token.includes(\"(\")) {\n                        var tokens = token.split(\".\");\n                        option = tokens[0] + \".\";\n                        token = tokens[1];\n                        continue;\n                    }\n                    option = token;\n                } else {\n                    propName = propName ? propName += token : token;\n                }\n                token = next();\n            }\n            var name = propName ? option.concat(propName) : option;\n            var optionValue = parseOptionValue(parent, name);\n            propName = propName && propName[0] === \".\" ? propName.slice(1) : propName;\n            option = option && option[option.length - 1] === \".\" ? option.slice(0, -1) : option;\n            setParsedOption(parent, option, optionValue, propName);\n    }\n\n    function parseOptionValue(parent, name) {\n        // { a: \"foo\" b { c: \"bar\" } }\n        if (skip(\"{\", true)) {\n            var objectResult = {};\n\n            while (!skip(\"}\", true)) {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next())) {\n                    throw illegal(token, \"name\");\n                }\n                if (token === null) {\n                  throw illegal(token, \"end of input\");\n                }\n\n                var value;\n                var propName = token;\n\n                skip(\":\", true);\n\n                if (peek() === \"{\") {\n                    // option (my_option) = {\n                    //     repeated_value: [ \"foo\", \"bar\" ]\n                    // };\n                    value = parseOptionValue(parent, name + \".\" + token);\n                } else if (peek() === \"[\") {\n                    value = [];\n                    var lastValue;\n                    if (skip(\"[\", true)) {\n                        do {\n                            lastValue = readValue(true);\n                            value.push(lastValue);\n                        } while (skip(\",\", true));\n                        skip(\"]\");\n                        if (typeof lastValue !== \"undefined\") {\n                            setOption(parent, name + \".\" + token, lastValue);\n                        }\n                    }\n                } else {\n                    value = readValue(true);\n                    setOption(parent, name + \".\" + token, value);\n                }\n\n                var prevValue = objectResult[propName];\n\n                if (prevValue)\n                    value = [].concat(prevValue).concat(value);\n\n                objectResult[propName] = value;\n\n                // Semicolons and commas can be optional\n                skip(\",\", true);\n                skip(\";\", true);\n            }\n\n            return objectResult;\n        }\n\n        var simpleValue = readValue(true);\n        setOption(parent, name, simpleValue);\n        return simpleValue;\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (ptr === parent && /^features\\./.test(name)) {\n            topLevelOptions[name] = value;\n            return;\n        }\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function setParsedOption(parent, name, value, propName) {\n        if (parent.setParsedOption)\n            parent.setParsedOption(name, value, propName);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token)) {\n                return;\n            }\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n        if (parent === ptr) {\n            topLevelObjects.push(service);\n        }\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                    parseField(parent, token, reference);\n                    break;\n\n                case \"optional\":\n                    /* istanbul ignore if */\n                    if (edition === \"proto3\") {\n                        parseField(parent, \"proto3_optional\", reference);\n                    } else {\n                        parseField(parent, \"optional\", reference);\n                    }\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (edition === \"proto2\" || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"edition\":\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n                parseEdition();\n                break;\n\n            case \"option\":\n                parseOption(ptr, token);\n                skip(\";\", true);\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    resolveFileFeatures();\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup(buffer) {\n            return (Reader.create = function create_buffer(buffer) {\n                return util.Buffer.isBuffer(buffer)\n                    ? new BufferReader(buffer)\n                    /* istanbul ignore next */\n                    : create_array(buffer);\n            })(buffer);\n        }\n        /* istanbul ignore next */\n        : create_array;\n};\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = create();\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/**\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n\n    if (start === end) { // fix for IE 10/Win8 and others' subarray returning array of size 1\n        var nativeBuffer = util.Buffer;\n        return nativeBuffer\n            ? nativeBuffer.alloc(0)\n            : new this.buf.constructor(0);\n    }\n    return this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n    Reader.create = create();\n    BufferReader._configure();\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = B<PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\nBufferReader._configure = function () {\n    /* istanbul ignore else */\n    if (util.Buffer)\n        BufferReader.prototype._slice = util.Buffer.prototype.slice;\n};\n\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice\n        ? this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len))\n        : this.buf.toString(\"utf-8\", this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {Buffer} Value read\n */\n\nBufferReader._configure();\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n\n    /**\n     * Edition, defaults to proto2 if unspecified.\n     * @type {string}\n     * @private\n     */\n    this._edition = \"proto2\";\n\n    /**\n     * Global lookup cache of fully qualified names.\n     * @type {Object.<string,ReflectionObject>}\n     * @private\n     */\n    this._fullyQualifiedObjects = {};\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Namespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested).resolveAll();\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n/**\n * Fetch content from file path or url\n * This method exists so you can override it with your own logic.\n * @function\n * @param {string} path File path or url\n * @param {FetchCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.fetch = util.fetch;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback) {\n        return util.asPromise(load, self, filename, options);\n    }\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback) {\n            return;\n        }\n        if (sync) {\n            throw err;\n        }\n        if (root) {\n            root.resolveAll();\n        }\n        var cb = callback;\n        callback = null;\n        cb(err, root);\n    }\n\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname;\n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i]))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i]))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued) {\n            finish(null, self); // only once anyway\n        }\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n        filename = getBundledFileName(filename) || filename;\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1) {\n            return;\n        }\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync) {\n                process(filename, common[filename]);\n            } else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            self.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback) {\n                    return; // terminated meanwhile\n                }\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename)) {\n        filename = [ filename ];\n    }\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n    if (sync) {\n        self.resolveAll();\n        return self;\n    }\n    if (!queued) {\n        finish(null, self);\n    }\n\n    return self;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        //do not allow to extend same field twice to prevent the error\n        if (extendedType.get(sisterField.name)) {\n            return true;\n        }\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    if (object instanceof Type || object instanceof Enum || object instanceof Field) {\n        // Only store types and enums for quick lookup during resolve.\n        this._fullyQualifiedObjects[object.fullName] = object;\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n\n    delete this._fullyQualifiedObjects[object.fullName];\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available across modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    if (json.edition)\n        service._edition = json.edition;\n    service.comment = json.comment;\n    service._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\" , this._editionToJSON(),\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    Namespace.prototype.resolve.call(this);\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return this;\n};\n\n/**\n * @override\n */\nService.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n\n    edition = this._edition || edition;\n\n    Namespace.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.methodsArray.forEach(method => {\n        method._resolveFeaturesRecursive(edition);\n    });\n    return this;\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        lastCommentLine = 0,\n        comments = {};\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @param {boolean} isLeading set if a leading comment\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end, isLeading) {\n        var comment = {\n            type: source.charAt(start++),\n            lineEmpty: false,\n            leading: isLeading,\n        };\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                comment.lineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        comment.text = lines\n            .join(\"\\n\")\n            .trim();\n\n        comments[line] = comment;\n        lastCommentLine = line;\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        var isComment = /^\\s*\\/\\//.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc,\n            isLeadingComment = offset === 0;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\") {\n                    isLeadingComment = true;\n                    ++line;\n                }\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1, isLeadingComment);\n                            // Trailing comment cannot not be multi-line,\n                            // so leading comment state should be reset to handle potential next comments\n                            isLeadingComment = true;\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset - 1)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                                if (!isLeadingComment) {\n                                    // Trailing comment cannot not be multi-line\n                                    break;\n                                }\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset, isLeadingComment);\n                            isLeadingComment = true;\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2, isLeadingComment);\n                        isLeadingComment = true;\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        var comment;\n        if (trailingLine === undefined) {\n            comment = comments[line - 1];\n            delete comments[line - 1];\n            if (comment && (alternateCommentMode || comment.type === \"*\" || comment.lineEmpty)) {\n                ret = comment.leading ? comment.text : null;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (lastCommentLine < trailingLine) {\n                peek();\n            }\n            comment = comments[trailingLine];\n            delete comments[trailingLine];\n            if (comment && !comment.lineEmpty && (alternateCommentMode || comment.type === \"/\")) {\n                ret = comment.leading ? null : comment.text;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {Array.<number[]|string>} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    if (json.edition)\n        type._edition = json.edition;\n    type._defaultEdition = \"proto3\";  // For backwards-compatibility.\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"edition\"    , this._editionToJSON(),\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    if (!this._needsRecursiveResolve) return this;\n\n    Namespace.prototype.resolveAll.call(this);\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    return this;\n};\n\n/**\n * @override\n */\nType.prototype._resolveFeaturesRecursive = function _resolveFeaturesRecursive(edition) {\n    if (!this._needsRecursiveFeatureResolution) return this;\n\n    edition = this._edition || edition;\n\n    Namespace.prototype._resolveFeaturesRecursive.call(this, edition);\n    this.oneofsArray.forEach(oneof => {\n        oneof._resolveFeatures(edition);\n    });\n    this.fieldsArray.forEach(field => {\n        field._resolveFeatures(edition);\n    });\n    return this;\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n\n/**\n * Sets the value of a property by property path. If a value already exists, it is turned to an array\n * @param {Object.<string,*>} dst Destination object\n * @param {string} path dot '.' delimited path of the property to set\n * @param {Object} value the value to set\n * @param {boolean|undefined} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {Object.<string,*>} Destination object\n */\nutil.setProperty = function setProperty(dst, path, value, ifNotSet) {\n    function setProp(dst, path, value) {\n        var part = path.shift();\n        if (part === \"__proto__\" || part === \"prototype\") {\n          return dst;\n        }\n        if (path.length > 0) {\n            dst[part] = setProp(dst[part] || {}, path, value);\n        } else {\n            var prevValue = dst[part];\n            if (prevValue && ifNotSet)\n                return dst;\n            if (prevValue)\n                value = [].concat(prevValue).concat(value);\n            dst[part] = value;\n        }\n        return dst;\n    }\n\n    if (typeof dst !== \"object\")\n        throw TypeError(\"dst must be an object\");\n    if (!path)\n        throw TypeError(\"path must be specified\");\n\n    path = path.split(\".\");\n    return setProp(dst, path, value);\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {Long|number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/**\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n */\nutil.isNode = Boolean(typeof global !== \"undefined\"\n                   && global\n                   && global.process\n                   && global.process.versions\n                   && global.process.versions.node);\n\n/**\n * Global object reference.\n * @memberof util\n * @type {Object}\n */\nutil.global = util.isNode && global\n           || typeof window !== \"undefined\" && window\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/**\n * Any compatible Long instance.\n * This is a minimal stand-alone definition of a Long instance. The actual type is that exported by long.js.\n * @interface Long\n * @property {number} low Low bits\n * @property {number} high High bits\n * @property {boolean} unsigned Whether unsigned or not\n */\n\n/**\n * Long.js's Long class if available.\n * @type {Constructor<Long>}\n */\nutil.Long = /* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\");\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/**\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/**\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: new Error().stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    CustomError.prototype = Object.create(Error.prototype, {\n        constructor: {\n            value: CustomError,\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n        name: {\n            get: function get() { return name; },\n            set: undefined,\n            enumerable: false,\n            // configurable: false would accurately preserve the behavior of\n            // the original, but I'm guessing that was not intentional.\n            // For an actual error subclass, this property would\n            // be configurable.\n            configurable: true,\n        },\n        toString: {\n            value: function value() { return this.name + \": \" + this.message; },\n            writable: true,\n            enumerable: false,\n            configurable: true,\n        },\n    });\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) { gen\n            (\"if(!Array.isArray(%s))\", ref)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", ref);\n                genVerifyValue(gen, field, i, ref + \"[i]\")\n            (\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n             // Only use fully qualified type name after the last '/'\n            var name = object[\"@type\"].substring(object[\"@type\"].lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].slice(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                if (type_url.indexOf(\"/\") === -1) {\n                    type_url = \"/\" + type_url;\n                }\n                return this.create({\n                    type_url: type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // Default prefix\n        var googleApi = \"type.googleapis.com/\";\n        var prefix = \"\";\n        var name = \"\";\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            // Separate the prefix used\n            prefix = message.type_url.substring(0, message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            var messageName = message.$type.fullName[0] === \".\" ?\n                message.$type.fullName.slice(1) : message.$type.fullName;\n            // Default to type.googleapis.com prefix if no prefix is used\n            if (prefix === \"\") {\n                prefix = googleApi;\n            }\n            name = prefix + messageName;\n            object[\"@type\"] = name;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\nvar create = function create() {\n    return util.Buffer\n        ? function create_buffer_setup() {\n            return (Writer.create = function create_buffer() {\n                return new BufferWriter();\n            })();\n        }\n        /* istanbul ignore next */\n        : function create_array() {\n            return new Writer();\n        };\n};\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = create();\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {Long|number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n    Writer.create = create();\n    BufferWriter._configure();\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\nBufferWriter._configure = function () {\n    /**\n     * Allocates a buffer of the specified size.\n     * @function\n     * @param {number} size Buffer size\n     * @returns {Buffer} Buffer\n     */\n    BufferWriter.alloc = util._Buffer_allocUnsafe;\n\n    BufferWriter.writeBytesBuffer = util.Buffer && util.Buffer.prototype instanceof Uint8Array && util.Buffer.prototype.set.name === \"set\"\n        ? function writeBytesBuffer_set(val, buf, pos) {\n          buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n          // also works for plain array values\n        }\n        /* istanbul ignore next */\n        : function writeBytesBuffer_copy(val, buf, pos) {\n          if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n          else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n        };\n};\n\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(BufferWriter.writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else if (buf.utf8Write)\n        buf.utf8Write(val, pos);\n    else\n        buf.write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = util.Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n\nBufferWriter._configure();\n"], "sourceRoot": "."}