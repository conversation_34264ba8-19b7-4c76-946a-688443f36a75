import { useState, useEffect, useCallback } from 'react';

export interface DockerService {
  name: string;
  containerName: string;
  url: string | null;
  icon: string;
  description: string;
  status: 'running' | 'stopped' | 'error' | 'not_found' | 'loading';
  state: string;
  uptime: string | null;
  health: 'healthy' | 'unhealthy' | 'starting' | 'no-healthcheck' | 'unknown';
  lastChecked: string;
  created?: number;
  image?: string;
  ports?: Array<{
    IP?: string;
    PrivatePort: number;
    PublicPort?: number;
    Type: string;
  }>;
  error?: string;
}

export interface DockerInfo {
  version: string;
  apiVersion: string;
  containers: number;
  containersRunning: number;
  containersPaused: number;
  containersStopped: number;
  images: number;
}

interface UseDockerServicesReturn {
  services: DockerService[];
  dockerInfo: DockerInfo | null;
  loading: boolean;
  error: string | null;
  refreshServices: () => Promise<void>;
  refreshDockerInfo: () => Promise<void>;
  lastUpdated: Date | null;
}

const API_BASE_URL = 'https://api.msarknet.me/api'; // Backend en subdominio separado

export const useDockerServices = (autoRefresh = true, refreshInterval = 30000): UseDockerServicesReturn => {
  const [services, setServices] = useState<DockerService[]>([]);
  const [dockerInfo, setDockerInfo] = useState<DockerInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchServices = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch(`${API_BASE_URL}/services/status`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setServices(data.services);
        setLastUpdated(new Date());
      } else {
        throw new Error(data.error || 'Failed to fetch services');
      }
    } catch (err) {
      console.error('Error fetching services:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');

      // En caso de error, mantener servicios con estado de error
      setServices(prev => prev.map(service => ({
        ...service,
        status: 'error' as const,
        state: 'Connection error',
        health: 'unknown' as const,
        lastChecked: new Date().toISOString()
      })));
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchDockerInfo = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/docker/info`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setDockerInfo(data.docker);
      } else {
        throw new Error(data.error || 'Failed to fetch Docker info');
      }
    } catch (err) {
      console.error('Error fetching Docker info:', err);
      // No establecer error aquí para no interferir con el estado principal
    }
  }, []);

  const refreshServices = useCallback(async () => {
    setLoading(true);
    await fetchServices();
  }, [fetchServices]);

  const refreshDockerInfo = useCallback(async () => {
    await fetchDockerInfo();
  }, [fetchDockerInfo]);

  // Efecto inicial para cargar datos
  useEffect(() => {
    fetchServices();
    fetchDockerInfo();
  }, [fetchServices, fetchDockerInfo]);

  // Efecto para auto-refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchServices();
      fetchDockerInfo();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchServices, fetchDockerInfo]);

  return {
    services,
    dockerInfo,
    loading,
    error,
    refreshServices,
    refreshDockerInfo,
    lastUpdated
  };
};

// Hook para obtener el estado de un servicio específico
export const useDockerService = (serviceName: string) => {
  const [service, setService] = useState<DockerService | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchService = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/services/${serviceName}/status`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setService(data.service);
      } else {
        throw new Error(data.error || 'Failed to fetch service');
      }
    } catch (err) {
      console.error(`Error fetching service ${serviceName}:`, err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [serviceName]);

  useEffect(() => {
    fetchService();
  }, [fetchService]);

  return {
    service,
    loading,
    error,
    refresh: fetchService
  };
};

// Utilidad para obtener el color del badge según el estado
export const getStatusColor = (status: DockerService['status']): 'success' | 'warning' | 'danger' | 'info' => {
  switch (status) {
    case 'running':
      return 'success';
    case 'stopped':
      return 'warning';
    case 'error':
    case 'not_found':
      return 'danger';
    case 'loading':
    default:
      return 'info';
  }
};

// Utilidad para obtener el texto del estado
export const getStatusText = (status: DockerService['status']): string => {
  switch (status) {
    case 'running':
      return 'Activo';
    case 'stopped':
      return 'Detenido';
    case 'error':
      return 'Error';
    case 'not_found':
      return 'No encontrado';
    case 'loading':
      return 'Cargando...';
    default:
      return 'Desconocido';
  }
};

// Utilidad para obtener el color del health
export const getHealthColor = (health: DockerService['health']): 'success' | 'warning' | 'danger' | 'info' => {
  switch (health) {
    case 'healthy':
      return 'success';
    case 'starting':
      return 'warning';
    case 'unhealthy':
      return 'danger';
    case 'no-healthcheck':
    case 'unknown':
    default:
      return 'info';
  }
};
