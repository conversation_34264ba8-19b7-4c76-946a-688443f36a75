import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import { Button } from 'primereact/button';
import { ProgressSpinner } from 'primereact/progressspinner';
import { Message } from 'primereact/message';
import { useDockerServices, getStatusColor, getStatusText, getHealthColor } from './hooks/useDockerServices';
import "./App.scss";

function App() {
  const {
    services,
    dockerInfo,
    loading,
    error,
    refreshServices,
    lastUpdated
  } = useDockerServices(true, 30000);

  const getStatusBadge = (service: any) => {
    const statusColor = getStatusColor(service.status);
    const statusText = getStatusText(service.status);

    return (
      <div className="flex flex-column gap-1">
        <Badge value={statusText} severity={statusColor} className="text-xs" />
        {service.health !== 'no-healthcheck' && service.health !== 'unknown' && (
          <Badge
            value={service.health}
            severity={getHealthColor(service.health)}
            className="text-xs"
          />
        )}
      </div>
    );
  };

  const serviceCardTemplate = (service: any) => {
    return (
      <div className="service-card-content">
        <div className="flex align-items-center justify-content-between mb-3">
          <h3 className="service-title m-0">
            <span className="service-icon mr-2">{service.icon}</span>
            {service.name}
          </h3>
          {getStatusBadge(service)}
        </div>
        <p className="service-description text-600 mb-3">{service.description}</p>

        {/* Información adicional del contenedor */}
        {service.uptime && (
          <p className="service-uptime text-xs text-500 mb-2">
            <i className="pi pi-clock mr-1"></i>
            {service.uptime}
          </p>
        )}

        {service.url ? (
          <a
            href={service.url}
            target="_blank"
            rel="noopener noreferrer"
            className="service-link"
          >
            Abrir Servicio →
          </a>
        ) : (
          <span className="service-link-disabled text-400">
            Servicio interno (sin URL)
          </span>
        )}
      </div>
    );
  };

  // Mostrar loading inicial
  if (loading && services.length === 0) {
    return (
      <div className="app-container">
        <div className="main-content max-w-6xl mx-auto p-4 text-center">
          <h1 className="app-title text-6xl font-bold mb-3">🚀 MSarkNet</h1>
          <ProgressSpinner />
          <p className="text-white mt-3">Cargando estado de servicios...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="app-container">
      <div className="main-content max-w-6xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-6">
          {/* Status Badge con información de Docker */}
          <div className="status-container p-3 border-round-lg bg-green-50 border-1 border-green-200 mb-3">
            <span className="text-green-800 font-semibold">
              ✅ Monitoreo de contenedores Docker activo
            </span>
            <br />
            {dockerInfo && (
              <span className="text-green-600 text-sm">
                {dockerInfo.containersRunning} de {dockerInfo.containers} contenedores ejecutándose
              </span>
            )}
          </div>

          {/* Error message */}
          {error && (
            <Message
              severity="error"
              text={`Error: ${error}`}
              className="mb-3"
            />
          )}

          {/* Refresh button and last updated */}
          <div className="flex align-items-center justify-content-center gap-3 mb-4">
            <Button
              icon="pi pi-refresh"
              label="Actualizar"
              onClick={refreshServices}
              loading={loading}
              size="small"
              className="refresh-button"
            />
            {lastUpdated && (
              <span className="text-white text-sm opacity-80">
                Última actualización: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid">
          {services.map((service, index) => (
            <div key={index} className="col-12 md:col-6 lg:col-4">
              <Card
                className="service-card h-full cursor-pointer transition-all transition-duration-300 hover:shadow-4"
                pt={{
                  body: { className: 'p-4' },
                  content: { className: 'p-0' }
                }}
              >
                {serviceCardTemplate(service)}
              </Card>
            </div>
          ))}
        </div>

        {/* Docker Info & Tech Stack */}
        <div className="tech-stack-container mt-6 text-center">
          {dockerInfo && (
            <div className="docker-info-container mb-4 p-3 border-round-lg bg-blue-50 border-1 border-blue-200">
              <h4 className="text-blue-800 font-semibold mb-2">🐳 Información de Docker</h4>
              <div className="flex flex-wrap justify-content-center gap-2 text-sm">
                <Badge value={`Versión: ${dockerInfo.version}`} severity="info" />
                <Badge value={`Contenedores: ${dockerInfo.containers}`} severity="info" />
                <Badge value={`Ejecutándose: ${dockerInfo.containersRunning}`} severity="success" />
                <Badge value={`Detenidos: ${dockerInfo.containersStopped}`} severity="warning" />
                <Badge value={`Imágenes: ${dockerInfo.images}`} severity="info" />
              </div>
            </div>
          )}

          <h3 className="text-lg font-semibold mb-3">Stack Tecnológico:</h3>
          <div className="flex flex-wrap justify-content-center gap-2">
            {['Docker', 'Traefik', 'React', 'Node.js', 'PrimeReact', 'SSL/TLS', 'Nginx'].map((tech) => (
              <Badge
                key={tech}
                value={tech}
                className="tech-badge p-2 text-sm"
                severity="info"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
