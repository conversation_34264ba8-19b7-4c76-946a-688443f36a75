// PrimeReact Core CSS
@import 'primereact/resources/primereact.min.css';

// PrimeReact Theme - Elige uno de estos temas:
// <PERSON><PERSON> (recomendado para empezar)
@import 'primereact/resources/themes/lara-light-blue/theme.css';

// Alternativamente, puedes usar otros temas como:
// @import 'primereact/resources/themes/lara-dark-blue/theme.css';
// @import 'primereact/resources/themes/bootstrap4-light-blue/theme.css';
// @import 'primereact/resources/themes/material-light-blue/theme.css';
// @import 'primereact/resources/themes/fluent-light/theme.css';

// PrimeIcons (iconos de PrimeReact)
@import 'primeicons/primeicons.css';

// PrimeFlex (sistema de grid y utilidades CSS)
@import 'primeflex/primeflex.scss';

body {
  margin: 0;
}