<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentación - MSarkNet</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 2rem;
            margin: -2rem -2rem 2rem -2rem;
            border-radius: 0 0 10px 10px;
        }

        .nav {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 1rem;
        }

        .nav a {
            color: #495057;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .nav a:hover {
            background: #e9ecef;
            color: #007bff;
        }

        .section {
            background: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
        }

        .endpoint {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin: 1rem 0;
        }

        .method {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 3px;
            font-weight: bold;
            font-size: 0.8rem;
            margin-right: 0.5rem;
        }

        .get { background: #28a745; color: white; }
        .post { background: #007bff; color: white; }
        .put { background: #ffc107; color: black; }
        .delete { background: #dc3545; color: white; }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        .table th, .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #b8daff;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 Documentación de MSarkNet</h1>
        <p>Guía completa para el desarrollo local con Traefik</p>
    </div>

    <nav class="nav">
        <a href="#traefik">🚀 Traefik Setup</a>
        <a href="#apis">🔧 APIs</a>
        <a href="#docker">🐳 Docker</a>
        <a href="#ssl">🔒 SSL/TLS</a>
        <a href="#troubleshooting">🔍 Troubleshooting</a>
    </nav>

    <section id="traefik" class="section">
        <h2>🚀 Configuración de Traefik</h2>
        <p>Traefik está configurado para enrutar automáticamente el tráfico HTTPS usando certificados locales.</p>

        <div class="alert alert-info">
            <strong>💡 Tip:</strong> Todos los servicios están disponibles a través de HTTPS en el dominio <code>*.msarknet.me</code>
        </div>

        <h3>Servicios Disponibles:</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>Servicio</th>
                    <th>URL</th>
                    <th>Descripción</th>
                    <th>Estado</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Aplicación Principal</td>
                    <td><code>https://msarknet.me</code></td>
                    <td>Frontend principal</td>
                    <td>✅ Activo</td>
                </tr>
                <tr>
                    <td>Dashboard Traefik</td>
                    <td><code>https://traefik.msarknet.me</code></td>
                    <td>Panel de control</td>
                    <td>✅ Activo</td>
                </tr>
                <tr>
                    <td>Grafana</td>
                    <td><code>https://grafana.msarknet.me</code></td>
                    <td>Métricas y dashboards</td>
                    <td>✅ Activo</td>
                </tr>
                <tr>
                    <td>Prometheus</td>
                    <td><code>https://prom.msarknet.me</code></td>
                    <td>Monitorización</td>
                    <td>✅ Activo</td>
                </tr>
            </tbody>
        </table>
    </section>

    <section id="apis" class="section">
        <h2>🔧 API Endpoints</h2>
        <p>Documentación de los endpoints disponibles para desarrollo.</p>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api</code>
            <p>Endpoint principal de la API - devuelve información del servicio</p>
        </div>

        <div class="endpoint">
            <span class="method get">GET</span>
            <code>/api/health</code>
            <p>Health check del servicio</p>
        </div>

        <div class="endpoint">
            <span class="method post">POST</span>
            <code>/api/data</code>
            <p>Endpoint para recibir datos (simulado)</p>
        </div>

        <h3>Ejemplo de respuesta:</h3>
        <div class="code-block">
<pre>{
  "service": "API Backend Service",
  "version": "1.0.0",
  "timestamp": "2025-09-12T10:30:00Z",
  "environment": "development",
  "ssl": true,
  "cors_enabled": true
}</pre>
        </div>
    </section>

    <section id="docker" class="section">
        <h2>🐳 Configuración Docker</h2>
        <p>Comandos útiles para gestionar el entorno de desarrollo.</p>

        <h3>Comandos principales:</h3>
        <div class="code-block">
<pre># Iniciar todos los servicios
docker compose up -d

# Ver logs de Traefik
docker compose logs -f traefik

# Reiniciar un servicio específico
docker compose restart main-app

# Escalar un servicio
docker compose up -d --scale api-service=3

# Parar todos los servicios
docker compose down</pre>
        </div>

        <h3>Red Docker:</h3>
        <table class="table">
            <tr><td>Nombre de red</td><td><code>proxy</code></td></tr>
            <tr><td>Driver</td><td>bridge</td></tr>
            <tr><td>Subnet</td><td>**********/16</td></tr>
        </table>
    </section>

    <section id="ssl" class="section">
        <h2>🔒 Certificados SSL/TLS</h2>
        <p>Configuración de certificados para desarrollo local seguro.</p>

        <div class="alert alert-warning">
            <strong>⚠️ Importante:</strong> Los certificados son autofirmados para desarrollo local. En producción usar Let's Encrypt.
        </div>

        <h3>Generar nuevos certificados:</h3>
        <div class="code-block">
<pre># Hacer ejecutable el script
chmod +x generate-certs.sh

# Ejecutar generación de certificados
./generate-certs.sh</pre>
        </div>

        <h3>Configuración de navegador:</h3>
        <ol>
            <li>Accede a cualquier servicio HTTPS</li>
            <li>Haz clic en "Avanzado" cuando aparezca la advertencia</li>
            <li>Selecciona "Continuar a msarknet.me (no es seguro)"</li>
            <li>El certificado se recordará para futuras visitas</li>
        </ol>
    </section>

    <section id="troubleshooting" class="section">
        <h2>🔍 Troubleshooting</h2>

        <h3>Problemas comunes:</h3>

        <h4>❌ Error: "This site can't be reached"</h4>
        <ul>
            <li>Verificar que los servicios están ejecutándose: <code>docker compose ps</code></li>
            <li>Comprobar el archivo <code>/etc/hosts</code></li>
            <li>Reiniciar Traefik: <code>docker compose restart traefik</code></li>
        </ul>

        <h4>❌ Error de certificado SSL</h4>
        <ul>
            <li>Regenerar certificados: <code>./generate-certs.sh</code></li>
            <li>Limpiar caché del navegador</li>
            <li>Verificar que los archivos están en <code>./certs/</code></li>
        </ul>

        <h4>❌ Servicios no responden</h4>
        <ul>
            <li>Verificar logs: <code>docker compose logs [servicio]</code></li>
            <li>Comprobar red Docker: <code>docker network ls</code></li>
            <li>Reiniciar completamente: <code>docker compose down && docker compose up -d</code></li>
        </ul>

        <h3>Comandos útiles para debugging:</h3>
        <div class="code-block">
<pre># Ver estado de todos los contenedores
docker compose ps

# Inspeccionar la red proxy
docker network inspect proxy

# Ver configuración de Traefik
curl -s http://localhost:8080/api/rawdata | jq

# Test de conectividad
curl -k -H "Host: msarknet.me" https://localhost/</pre>
        </div>
    </section>

    🔑 Orden recomendado dentro de un servicio (docker-compose.yml):
container_name
image / build
command / entrypoint
restart
environment / env_file
ports
volumes
networks
depends_on
healthcheck
labels
Otros (ulimits, secrets, etc.)

    <footer style="text-align: center; margin-top: 3rem; padding: 2rem; color: #6c757d;">
        <p>📝 Documentación generada para el entorno de desarrollo local</p>
        <p>🔧 Stack: React + Node.js + Python + Docker + Traefik + Kubernetes</p>
    </footer>
</body>
</html>
