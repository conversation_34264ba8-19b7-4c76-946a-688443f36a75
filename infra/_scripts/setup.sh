#!/bin/bash

echo "🚀 Configurando entorno Traefik + <PERSON>ack para MSarkNet"
echo "============================================================="
echo -e ""

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Cargar funciones auxiliares
if [ -f "_scripts/hosts-functions.sh" ]; then
    source _scripts/hosts-functions.sh
else
    echo -e "${RED}Error: Archivo _scripts/hosts-functions.sh no encontrado${NC}"
    exit 1
fi

# Función para imprimir mensajes con colores
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_cerebro() {
    echo -e "${PURPLE}🧠 $1${NC}"
}

# Validar archivo de hosts
print_info "Step 1: Validando archivo de configuración de hosts..."
if ! validate_hosts_file; then
    print_error "El archivo de hosts tiene errores. Corrígelos antes de continuar."
    exit 1
fi
print_status "Archivo de hosts validado correctamente"
echo -e ""

# Verificar si Docker está instalado
print_info "Step 2: Verificando Docker..."
if ! command -v docker &> /dev/null; then
    print_error "Docker no está instalado. Por favor, instala Docker primero."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose no está instalado. Por favor, instala Docker Compose primero."
    exit 1
fi

print_status "Docker verificado"
echo -e ""

# Verificar archivo /etc/hosts
print_info "Step 3: Verificando configuración de /etc/hosts..."

# Obtener hosts faltantes usando las funciones auxiliares
MISSING_HOSTS_ARRAY=()
readarray -t MISSING_HOSTS_ARRAY < <(check_missing_hosts "127.0.0.1")

if [ ${#MISSING_HOSTS_ARRAY[@]} -ne 0 ]; then
    print_warning "Faltan entradas en /etc/hosts. Añade las siguientes líneas:"
    echo ""
    for entry in "${MISSING_HOSTS_ARRAY[@]}"; do
        echo -e "${YELLOW}$entry${NC}"
    done
    echo ""
    print_info "Para añadirlas automáticamente, ejecuta:"
    echo -e "${BLUE}sudo bash -c 'cat >> /etc/hosts << EOF"
    for entry in "${MISSING_HOSTS_ARRAY[@]}"; do
        echo "$entry"
    done
    echo "EOF'"
    echo -e "${NC}"

    read -p "¿Quieres que las añada automáticamente? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Añadiendo entradas a /etc/hosts..."
        for entry in "${MISSING_HOSTS_ARRAY[@]}"; do
            echo "$entry" | sudo tee -a /etc/hosts > /dev/null
        done
        print_status "Entradas añadidas a /etc/hosts"
        echo -e ""
    fi
else
    print_status "Configuración de /etc/hosts verificada"
fi

# Generar certificados SSL
print_info "Step 4: Generando certificados SSL..."
if [ -f "./_scripts/generate-certs.sh" ]; then
    chmod +x ./_scripts/generate-certs.sh
    bash ./_scripts/generate-certs.sh
else
    print_error "No se encontró ./_scripts/generate-certs.sh"
    exit 1
fi
print_status "Certificados SSL generados"
echo -e ""

# Crear red Docker si no existe
print_info "Step 5: Creando red Docker 'proxy'..."
docker network create proxy 2>/dev/null && print_status "Red Docker 'proxy' creada" || print_status "Red Docker 'proxy' ya existe"
echo -e ""

# Iniciar servicios
print_cerebro "Steo 6: Iniciando todos los servicios..."
docker compose up -d

# Esperar a que los servicios estén listos
print_info "Esperando a que los servicios estén listos..."
sleep 20

# Verificar estado de los servicios
print_info "Verificando estado de los servicios..."
if docker compose ps | grep -q "Up"; then
    print_status "Servicios iniciados correctamente"
else
    print_error "Algunos servicios no se iniciaron correctamente"
    docker compose ps
    exit 1
fi
echo -e ""

# Mostrar información de acceso usando las funciones auxiliares
echo ""
echo "🎉 ¡Configuración completada!"
echo "=============================="
echo ""

print_info "📋 Servicios disponibles:"

echo ""
echo "📊 Monitorización:"
get_hosts_by_category "monitoring" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
echo "🔧 Gestión:"
get_hosts_by_category "management" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
echo "🧪 Testing:"
get_hosts_by_category "testing" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
echo "🌐 Principal:"
get_hosts_by_category "main" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
echo "📚 Documentación:"
get_hosts_by_category "docs" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
echo "🌐 Webs:"
get_hosts_by_category "cerebro" | while read -r domain; do
    echo -e "   ${BLUE}https://$domain${NC}"
done

echo ""
print_warning "Los certificados son autofirmados. Acepta la advertencia de seguridad en el navegador."
echo ""

# Test básico de conectividad
print_info "Realizando tests de conectividad..."
