#!/bin/bash

# Script para generar certificados SSL locales para msarknet.me y subdominios
# Usa el archivo _scripts/hosts.txt como fuente de dominios

# Cargar funciones auxiliares
if [ -f "_scripts/hosts-functions.sh" ]; then
    source _scripts/hosts-functions.sh
else
    echo "Error: Archivo _scripts/hosts-functions.sh no encontrado"
    exit 1
fi

# Validar archivo de hosts
if ! validate_hosts_file; then
    echo "Error: El archivo de hosts tiene errores. Corrígelos antes de continuar."
    exit 1
fi

# Crear directorio para certificados
mkdir -p certs

echo "Generando certificados SSL para dominios configurados..."

# Crear archivo de configuración OpenSSL para certificado wildcard
cat > certs/msarknet.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = ES
ST = Madrid
L = Madrid
O = MSarkNet Local Development
OU = Development Team
CN = *.msarknet.me

[v3_req]
keyUsage = digitalSignature, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
$(generate_ssl_san_entries)
EOF

# Generar clave privada
openssl genrsa -out certs/msarknet.me.key 2048

# Generar certificado autofirmado
openssl req -new -x509 -key certs/msarknet.me.key \
    -out certs/msarknet.me.crt \
    -days 365 \
    -config certs/msarknet.conf \
    -extensions v3_req

# Establecer permisos correctos
chmod 600 certs/msarknet.me.key
chmod 644 certs/msarknet.me.crt

echo "✅ Certificados generados exitosamente:"
echo "   - Certificado: certs/msarknet.me.crt"
echo "   - Clave privada: certs/msarknet.me.key"
echo ""
echo "🔧 Para usar estos certificados:"
echo "   1. Ejecuta: docker compose up -d"
echo "   2. Accede a los servicios disponibles"
echo "   3. Acepta el certificado autofirmado en tu navegador"
echo ""
