#!/bin/bash

# Funciones auxiliares para gestión de hosts

HOSTS_FILE="_scripts/hosts.txt"

# Función para leer todos los hosts del archivo
get_all_hosts() {
    if [ ! -f "$HOSTS_FILE" ]; then
        echo "Error: Archivo $HOSTS_FILE no encontrado" >&2
        return 1
    fi

    # Leer líneas que no sean comentarios y extraer solo el dominio
    grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$' | cut -d'|' -f1
}

# Función para obtener hosts por categoría
get_hosts_by_category() {
    local category="$1"
    if [ -z "$category" ]; then
        echo "Error: Debe especificar una categoría" >&2
        return 1
    fi

    grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$' | grep "|$category$" | cut -d'|' -f1
}

# Función para generar entradas de /etc/hosts
generate_hosts_entries() {
    local ip="${1:-127.0.0.1}"

    while IFS='|' read -r domain description category; do
        # Saltar líneas vacías y comentarios
        [[ -z "$domain" || "$domain" =~ ^[[:space:]]*# ]] && continue

        echo "$ip $domain"
    done < <(grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$')
}

# Función para verificar hosts faltantes en /etc/hosts
check_missing_hosts() {
    local missing_hosts=()
    local ip="${1:-127.0.0.1}"

    while IFS='|' read -r domain description category; do
        [[ -z "$domain" || "$domain" =~ ^[[:space:]]*# ]] && continue

        local entry="$ip $domain"
        if ! grep -q "^$entry" /etc/hosts 2>/dev/null; then
            missing_hosts+=("$entry")
        fi
    done < <(grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$')

    printf '%s\n' "${missing_hosts[@]}"
}

# Función para generar SAN entries para certificados SSL
generate_ssl_san_entries() {
    local counter=1

    echo "DNS.$counter = localhost"
    ((counter++))

    while IFS='|' read -r domain description category; do
        [[ -z "$domain" || "$domain" =~ ^[[:space:]]*# ]] && continue

        echo "DNS.$counter = $domain"
        ((counter++))
    done < <(grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$')

    echo "IP.1 = 127.0.0.1"
}

# Función para mostrar información de hosts con formato
show_hosts_info() {
    local category_filter="$1"

    printf "%-30s %-40s %-15s\n" "DOMINIO" "DESCRIPCIÓN" "CATEGORÍA"
    printf "%-30s %-40s %-15s\n" "$(printf '%.0s-' {1..30})" "$(printf '%.0s-' {1..40})" "$(printf '%.0s-' {1..15})"

    while IFS='|' read -r domain description category; do
        [[ -z "$domain" || "$domain" =~ ^[[:space:]]*# ]] && continue

        # Filtrar por categoría si se especifica
        if [ -n "$category_filter" ] && [ "$category" != "$category_filter" ]; then
            continue
        fi

        printf "%-30s %-40s %-15s\n" "$domain" "$description" "$category"
    done < <(grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$')
}

# Función para generar URLs con protocolo
generate_urls() {
    local protocol="${1:-https}"

    while IFS='|' read -r domain description category; do
        [[ -z "$domain" || "$domain" =~ ^[[:space:]]*# ]] && continue

        echo "$protocol://$domain"
    done < <(grep -v '^#' "$HOSTS_FILE" | grep -v '^[[:space:]]*$')
}

# Función para validar el archivo de hosts
validate_hosts_file() {
    local errors=0
    local line_num=0

    if [ ! -f "$HOSTS_FILE" ]; then
        echo "Error: Archivo $HOSTS_FILE no encontrado"
        return 1
    fi

    while IFS= read -r line; do
        ((line_num++))

        # Saltar líneas vacías y comentarios
        [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]] && continue

        # Validar formato domain|description|category
        if [[ ! "$line" =~ ^[^|]+\|[^|]+\|[^|]+$ ]]; then
            echo "Error línea $line_num: Formato incorrecto - $line"
            ((errors++))
        fi

        # Validar que el dominio sea válido
        local domain=$(echo "$line" | cut -d'|' -f1)
        if [[ ! "$domain" =~ ^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]$ ]]; then
            echo "Error línea $line_num: Dominio inválido - $domain"
            ((errors++))
        fi
    done < "$HOSTS_FILE"

    if [ $errors -eq 0 ]; then
        echo "Archivo de hosts válido"
        return 0
    else
        echo "Se encontraron $errors errores"
        return 1
    fi
}
