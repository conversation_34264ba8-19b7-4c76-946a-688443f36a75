global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8082']

  - job_name: 'cerebro-be'
    static_configs:
      - targets: ['cerebro-be:5000']
    metrics_path: '/metrics'

  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
