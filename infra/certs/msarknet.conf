[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = ES
ST = Madrid
L = Madrid
O = MSarkNet Local Development
OU = Development Team
CN = *.msarknet.me

[v3_req]
keyUsage = digitalSignature, keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = grafana.msarknet.me
DNS.3 = prom.msarknet.me
DNS.4 = traefik.msarknet.me
DNS.5 = portainer.msarknet.me
DNS.6 = adminer.msarknet.me
DNS.7 = whoami.msarknet.me
DNS.8 = msarknet.me
DNS.9 = docs.msarknet.me
DNS.10 = api.msarknet.me
IP.1 = 127.0.0.1
